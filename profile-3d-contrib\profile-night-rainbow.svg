<svg xmlns="http://www.w3.org/2000/svg" width="1280" height="850" viewBox="0 0 1280 850"><style>* { font-family: "Ubuntu", "Helvetica", "Arial", sans-serif; }
.fill-fg { fill: #eeeeff; }
.stroke-fg { stroke: #eeeeff; }
.fill-bg { fill: #00000f; }
.stroke-bg { stroke: #00000f; }
.fill-strong { fill: rgb(255,200,55); }
.fill-weak { fill: #aaaaaa; }
.stroke-weak { stroke: #aaaaaa; }
.radar {
stroke-width: 4px;
stroke: rgb(255,200,55);
fill: rgb(255,200,55);
fill-opacity: 0.5;
}</style><rect x="0" y="0" width="1280" height="850" class="fill-bg"></rect><g><g transform="translate(140 154.18)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 25);rgb(77, 77, 25);rgb(25, 77, 25);rgb(25, 77, 77);rgb(25, 25, 77);rgb(77, 25, 77);rgb(77, 25, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 21);rgb(64, 64, 21);rgb(21, 64, 21);rgb(21, 64, 64);rgb(21, 21, 64);rgb(64, 21, 64);rgb(64, 21, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 18);rgb(54, 54, 18);rgb(18, 54, 18);rgb(18, 54, 54);rgb(18, 18, 54);rgb(54, 18, 54);rgb(54, 18, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(120 165.73)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 25);rgb(77, 77, 25);rgb(25, 77, 25);rgb(25, 77, 77);rgb(25, 25, 77);rgb(77, 25, 77);rgb(77, 25, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 21);rgb(64, 64, 21);rgb(21, 64, 21);rgb(21, 64, 64);rgb(21, 21, 64);rgb(64, 21, 64);rgb(64, 21, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 18);rgb(54, 54, 18);rgb(18, 54, 18);rgb(18, 54, 54);rgb(18, 18, 54);rgb(54, 18, 54);rgb(54, 18, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(100 177.27)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 25);rgb(77, 77, 25);rgb(25, 77, 25);rgb(25, 77, 77);rgb(25, 25, 77);rgb(77, 25, 77);rgb(77, 25, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 21);rgb(64, 64, 21);rgb(21, 64, 21);rgb(21, 64, 64);rgb(21, 21, 64);rgb(64, 21, 64);rgb(64, 21, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 18);rgb(54, 54, 18);rgb(18, 54, 18);rgb(18, 54, 54);rgb(18, 18, 54);rgb(54, 18, 54);rgb(54, 18, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(80 188.82)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 25);rgb(77, 77, 25);rgb(25, 77, 25);rgb(25, 77, 77);rgb(25, 25, 77);rgb(77, 25, 77);rgb(77, 25, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 21);rgb(64, 64, 21);rgb(21, 64, 21);rgb(21, 64, 64);rgb(21, 21, 64);rgb(64, 21, 64);rgb(64, 21, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 18);rgb(54, 54, 18);rgb(18, 54, 18);rgb(18, 54, 54);rgb(18, 18, 54);rgb(54, 18, 54);rgb(54, 18, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(60 200.37)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 25);rgb(77, 77, 25);rgb(25, 77, 25);rgb(25, 77, 77);rgb(25, 25, 77);rgb(77, 25, 77);rgb(77, 25, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 21);rgb(64, 64, 21);rgb(21, 64, 21);rgb(21, 64, 64);rgb(21, 21, 64);rgb(64, 21, 64);rgb(64, 21, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 18);rgb(54, 54, 18);rgb(18, 54, 18);rgb(18, 54, 54);rgb(18, 18, 54);rgb(54, 18, 54);rgb(54, 18, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(40 211.91)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 25);rgb(77, 77, 25);rgb(25, 77, 25);rgb(25, 77, 77);rgb(25, 25, 77);rgb(77, 25, 77);rgb(77, 25, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 21);rgb(64, 64, 21);rgb(21, 64, 21);rgb(21, 64, 64);rgb(21, 21, 64);rgb(64, 21, 64);rgb(64, 21, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 18);rgb(54, 54, 18);rgb(18, 54, 18);rgb(18, 54, 54);rgb(18, 18, 54);rgb(54, 18, 54);rgb(54, 18, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(20 223.46)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 25);rgb(77, 77, 25);rgb(25, 77, 25);rgb(25, 77, 77);rgb(25, 25, 77);rgb(77, 25, 77);rgb(77, 25, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 21);rgb(64, 64, 21);rgb(21, 64, 21);rgb(21, 64, 64);rgb(21, 21, 64);rgb(64, 21, 64);rgb(64, 21, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 18);rgb(54, 54, 18);rgb(18, 54, 18);rgb(18, 54, 54);rgb(18, 18, 54);rgb(54, 18, 54);rgb(54, 18, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(160 165.73)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 31);rgb(77, 71, 25);rgb(31, 77, 25);rgb(25, 77, 71);rgb(25, 31, 77);rgb(71, 25, 77);rgb(77, 25, 31)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 26);rgb(64, 59, 21);rgb(26, 64, 21);rgb(21, 64, 59);rgb(21, 26, 64);rgb(59, 21, 64);rgb(64, 21, 26)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 22);rgb(54, 49, 18);rgb(22, 54, 18);rgb(18, 54, 49);rgb(18, 22, 54);rgb(49, 18, 54);rgb(54, 18, 22)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(140 177.27)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 31);rgb(77, 71, 25);rgb(31, 77, 25);rgb(25, 77, 71);rgb(25, 31, 77);rgb(71, 25, 77);rgb(77, 25, 31)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 26);rgb(64, 59, 21);rgb(26, 64, 21);rgb(21, 64, 59);rgb(21, 26, 64);rgb(59, 21, 64);rgb(64, 21, 26)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 22);rgb(54, 49, 18);rgb(22, 54, 18);rgb(18, 54, 49);rgb(18, 22, 54);rgb(49, 18, 54);rgb(54, 18, 22)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(120 188.82)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 31);rgb(77, 71, 25);rgb(31, 77, 25);rgb(25, 77, 71);rgb(25, 31, 77);rgb(71, 25, 77);rgb(77, 25, 31)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 26);rgb(64, 59, 21);rgb(26, 64, 21);rgb(21, 64, 59);rgb(21, 26, 64);rgb(59, 21, 64);rgb(64, 21, 26)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 22);rgb(54, 49, 18);rgb(22, 54, 18);rgb(18, 54, 49);rgb(18, 22, 54);rgb(49, 18, 54);rgb(54, 18, 22)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(100 200.37)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 31);rgb(77, 71, 25);rgb(31, 77, 25);rgb(25, 77, 71);rgb(25, 31, 77);rgb(71, 25, 77);rgb(77, 25, 31)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 26);rgb(64, 59, 21);rgb(26, 64, 21);rgb(21, 64, 59);rgb(21, 26, 64);rgb(59, 21, 64);rgb(64, 21, 26)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 22);rgb(54, 49, 18);rgb(22, 54, 18);rgb(18, 54, 49);rgb(18, 22, 54);rgb(49, 18, 54);rgb(54, 18, 22)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(80 211.91)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 31);rgb(77, 71, 25);rgb(31, 77, 25);rgb(25, 77, 71);rgb(25, 31, 77);rgb(71, 25, 77);rgb(77, 25, 31)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 26);rgb(64, 59, 21);rgb(26, 64, 21);rgb(21, 64, 59);rgb(21, 26, 64);rgb(59, 21, 64);rgb(64, 21, 26)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 22);rgb(54, 49, 18);rgb(22, 54, 18);rgb(18, 54, 49);rgb(18, 22, 54);rgb(49, 18, 54);rgb(54, 18, 22)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(60 223.46)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 31);rgb(77, 71, 25);rgb(31, 77, 25);rgb(25, 77, 71);rgb(25, 31, 77);rgb(71, 25, 77);rgb(77, 25, 31)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 26);rgb(64, 59, 21);rgb(26, 64, 21);rgb(21, 64, 59);rgb(21, 26, 64);rgb(59, 21, 64);rgb(64, 21, 26)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 22);rgb(54, 49, 18);rgb(22, 54, 18);rgb(18, 54, 49);rgb(18, 22, 54);rgb(49, 18, 54);rgb(54, 18, 22)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(40 235.01)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 31);rgb(77, 71, 25);rgb(31, 77, 25);rgb(25, 77, 71);rgb(25, 31, 77);rgb(71, 25, 77);rgb(77, 25, 31)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 26);rgb(64, 59, 21);rgb(26, 64, 21);rgb(21, 64, 59);rgb(21, 26, 64);rgb(59, 21, 64);rgb(64, 21, 26)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 22);rgb(54, 49, 18);rgb(22, 54, 18);rgb(18, 54, 49);rgb(18, 22, 54);rgb(49, 18, 54);rgb(54, 18, 22)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(180 177.27)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 37);rgb(77, 65, 25);rgb(37, 77, 25);rgb(25, 77, 65);rgb(25, 37, 77);rgb(65, 25, 77);rgb(77, 25, 37)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 31);rgb(64, 54, 21);rgb(31, 64, 21);rgb(21, 64, 54);rgb(21, 31, 64);rgb(54, 21, 64);rgb(64, 21, 31)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 26);rgb(54, 45, 18);rgb(26, 54, 18);rgb(18, 54, 45);rgb(18, 26, 54);rgb(45, 18, 54);rgb(54, 18, 26)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(160 188.82)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 37);rgb(77, 65, 25);rgb(37, 77, 25);rgb(25, 77, 65);rgb(25, 37, 77);rgb(65, 25, 77);rgb(77, 25, 37)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 31);rgb(64, 54, 21);rgb(31, 64, 21);rgb(21, 64, 54);rgb(21, 31, 64);rgb(54, 21, 64);rgb(64, 21, 31)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 26);rgb(54, 45, 18);rgb(26, 54, 18);rgb(18, 54, 45);rgb(18, 26, 54);rgb(45, 18, 54);rgb(54, 18, 26)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(140 200.37)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 37);rgb(77, 65, 25);rgb(37, 77, 25);rgb(25, 77, 65);rgb(25, 37, 77);rgb(65, 25, 77);rgb(77, 25, 37)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 31);rgb(64, 54, 21);rgb(31, 64, 21);rgb(21, 64, 54);rgb(21, 31, 64);rgb(54, 21, 64);rgb(64, 21, 31)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 26);rgb(54, 45, 18);rgb(26, 54, 18);rgb(18, 54, 45);rgb(18, 26, 54);rgb(45, 18, 54);rgb(54, 18, 26)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(120 211.91)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 37);rgb(77, 65, 25);rgb(37, 77, 25);rgb(25, 77, 65);rgb(25, 37, 77);rgb(65, 25, 77);rgb(77, 25, 37)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 31);rgb(64, 54, 21);rgb(31, 64, 21);rgb(21, 64, 54);rgb(21, 31, 64);rgb(54, 21, 64);rgb(64, 21, 31)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 26);rgb(54, 45, 18);rgb(26, 54, 18);rgb(18, 54, 45);rgb(18, 26, 54);rgb(45, 18, 54);rgb(54, 18, 26)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(100 223.46)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 37);rgb(77, 65, 25);rgb(37, 77, 25);rgb(25, 77, 65);rgb(25, 37, 77);rgb(65, 25, 77);rgb(77, 25, 37)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 31);rgb(64, 54, 21);rgb(31, 64, 21);rgb(21, 64, 54);rgb(21, 31, 64);rgb(54, 21, 64);rgb(64, 21, 31)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 26);rgb(54, 45, 18);rgb(26, 54, 18);rgb(18, 54, 45);rgb(18, 26, 54);rgb(45, 18, 54);rgb(54, 18, 26)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(80 235.01)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 37);rgb(77, 65, 25);rgb(37, 77, 25);rgb(25, 77, 65);rgb(25, 37, 77);rgb(65, 25, 77);rgb(77, 25, 37)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 31);rgb(64, 54, 21);rgb(31, 64, 21);rgb(21, 64, 54);rgb(21, 31, 64);rgb(54, 21, 64);rgb(64, 21, 31)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 26);rgb(54, 45, 18);rgb(26, 54, 18);rgb(18, 54, 45);rgb(18, 26, 54);rgb(45, 18, 54);rgb(54, 18, 26)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(60 246.56)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 37);rgb(77, 65, 25);rgb(37, 77, 25);rgb(25, 77, 65);rgb(25, 37, 77);rgb(65, 25, 77);rgb(77, 25, 37)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 31);rgb(64, 54, 21);rgb(31, 64, 21);rgb(21, 64, 54);rgb(21, 31, 64);rgb(54, 21, 64);rgb(64, 21, 31)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 26);rgb(54, 45, 18);rgb(26, 54, 18);rgb(18, 54, 45);rgb(18, 26, 54);rgb(45, 18, 54);rgb(54, 18, 26)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(200 188.82)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 43);rgb(77, 59, 25);rgb(43, 77, 25);rgb(25, 77, 59);rgb(25, 43, 77);rgb(59, 25, 77);rgb(77, 25, 43)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 36);rgb(64, 49, 21);rgb(36, 64, 21);rgb(21, 64, 49);rgb(21, 36, 64);rgb(49, 21, 64);rgb(64, 21, 36)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 30);rgb(54, 41, 18);rgb(30, 54, 18);rgb(18, 54, 41);rgb(18, 30, 54);rgb(41, 18, 54);rgb(54, 18, 30)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(180 200.37)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 43);rgb(77, 59, 25);rgb(43, 77, 25);rgb(25, 77, 59);rgb(25, 43, 77);rgb(59, 25, 77);rgb(77, 25, 43)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 36);rgb(64, 49, 21);rgb(36, 64, 21);rgb(21, 64, 49);rgb(21, 36, 64);rgb(49, 21, 64);rgb(64, 21, 36)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 30);rgb(54, 41, 18);rgb(30, 54, 18);rgb(18, 54, 41);rgb(18, 30, 54);rgb(41, 18, 54);rgb(54, 18, 30)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(160 211.91)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 43);rgb(77, 59, 25);rgb(43, 77, 25);rgb(25, 77, 59);rgb(25, 43, 77);rgb(59, 25, 77);rgb(77, 25, 43)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 36);rgb(64, 49, 21);rgb(36, 64, 21);rgb(21, 64, 49);rgb(21, 36, 64);rgb(49, 21, 64);rgb(64, 21, 36)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 30);rgb(54, 41, 18);rgb(30, 54, 18);rgb(18, 54, 41);rgb(18, 30, 54);rgb(41, 18, 54);rgb(54, 18, 30)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(140 223.46)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 43);rgb(77, 59, 25);rgb(43, 77, 25);rgb(25, 77, 59);rgb(25, 43, 77);rgb(59, 25, 77);rgb(77, 25, 43)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 36);rgb(64, 49, 21);rgb(36, 64, 21);rgb(21, 64, 49);rgb(21, 36, 64);rgb(49, 21, 64);rgb(64, 21, 36)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 30);rgb(54, 41, 18);rgb(30, 54, 18);rgb(18, 54, 41);rgb(18, 30, 54);rgb(41, 18, 54);rgb(54, 18, 30)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(120 235.01)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 43);rgb(77, 59, 25);rgb(43, 77, 25);rgb(25, 77, 59);rgb(25, 43, 77);rgb(59, 25, 77);rgb(77, 25, 43)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 36);rgb(64, 49, 21);rgb(36, 64, 21);rgb(21, 64, 49);rgb(21, 36, 64);rgb(49, 21, 64);rgb(64, 21, 36)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 30);rgb(54, 41, 18);rgb(30, 54, 18);rgb(18, 54, 41);rgb(18, 30, 54);rgb(41, 18, 54);rgb(54, 18, 30)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(100 246.56)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 43);rgb(77, 59, 25);rgb(43, 77, 25);rgb(25, 77, 59);rgb(25, 43, 77);rgb(59, 25, 77);rgb(77, 25, 43)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 36);rgb(64, 49, 21);rgb(36, 64, 21);rgb(21, 64, 49);rgb(21, 36, 64);rgb(49, 21, 64);rgb(64, 21, 36)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 30);rgb(54, 41, 18);rgb(30, 54, 18);rgb(18, 54, 41);rgb(18, 30, 54);rgb(41, 18, 54);rgb(54, 18, 30)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(80 258.1)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 43);rgb(77, 59, 25);rgb(43, 77, 25);rgb(25, 77, 59);rgb(25, 43, 77);rgb(59, 25, 77);rgb(77, 25, 43)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 36);rgb(64, 49, 21);rgb(36, 64, 21);rgb(21, 64, 49);rgb(21, 36, 64);rgb(49, 21, 64);rgb(64, 21, 36)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 30);rgb(54, 41, 18);rgb(30, 54, 18);rgb(18, 54, 41);rgb(18, 30, 54);rgb(41, 18, 54);rgb(54, 18, 30)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(220 200.37)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 49);rgb(77, 53, 25);rgb(49, 77, 25);rgb(25, 77, 53);rgb(25, 49, 77);rgb(53, 25, 77);rgb(77, 25, 49)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 41);rgb(64, 44, 21);rgb(41, 64, 21);rgb(21, 64, 44);rgb(21, 41, 64);rgb(44, 21, 64);rgb(64, 21, 41)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 35);rgb(54, 37, 18);rgb(35, 54, 18);rgb(18, 54, 37);rgb(18, 35, 54);rgb(37, 18, 54);rgb(54, 18, 35)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(200 211.91)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 49);rgb(77, 53, 25);rgb(49, 77, 25);rgb(25, 77, 53);rgb(25, 49, 77);rgb(53, 25, 77);rgb(77, 25, 49)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 41);rgb(64, 44, 21);rgb(41, 64, 21);rgb(21, 64, 44);rgb(21, 41, 64);rgb(44, 21, 64);rgb(64, 21, 41)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 35);rgb(54, 37, 18);rgb(35, 54, 18);rgb(18, 54, 37);rgb(18, 35, 54);rgb(37, 18, 54);rgb(54, 18, 35)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(180 223.46)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 49);rgb(77, 53, 25);rgb(49, 77, 25);rgb(25, 77, 53);rgb(25, 49, 77);rgb(53, 25, 77);rgb(77, 25, 49)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 41);rgb(64, 44, 21);rgb(41, 64, 21);rgb(21, 64, 44);rgb(21, 41, 64);rgb(44, 21, 64);rgb(64, 21, 41)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 35);rgb(54, 37, 18);rgb(35, 54, 18);rgb(18, 54, 37);rgb(18, 35, 54);rgb(37, 18, 54);rgb(54, 18, 35)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(160 235.01)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 49);rgb(77, 53, 25);rgb(49, 77, 25);rgb(25, 77, 53);rgb(25, 49, 77);rgb(53, 25, 77);rgb(77, 25, 49)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 41);rgb(64, 44, 21);rgb(41, 64, 21);rgb(21, 64, 44);rgb(21, 41, 64);rgb(44, 21, 64);rgb(64, 21, 41)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 35);rgb(54, 37, 18);rgb(35, 54, 18);rgb(18, 54, 37);rgb(18, 35, 54);rgb(37, 18, 54);rgb(54, 18, 35)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(140 246.56)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 49);rgb(77, 53, 25);rgb(49, 77, 25);rgb(25, 77, 53);rgb(25, 49, 77);rgb(53, 25, 77);rgb(77, 25, 49)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 41);rgb(64, 44, 21);rgb(41, 64, 21);rgb(21, 64, 44);rgb(21, 41, 64);rgb(44, 21, 64);rgb(64, 21, 41)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 35);rgb(54, 37, 18);rgb(35, 54, 18);rgb(18, 54, 37);rgb(18, 35, 54);rgb(37, 18, 54);rgb(54, 18, 35)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(120 258.1)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 49);rgb(77, 53, 25);rgb(49, 77, 25);rgb(25, 77, 53);rgb(25, 49, 77);rgb(53, 25, 77);rgb(77, 25, 49)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 41);rgb(64, 44, 21);rgb(41, 64, 21);rgb(21, 64, 44);rgb(21, 41, 64);rgb(44, 21, 64);rgb(64, 21, 41)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 35);rgb(54, 37, 18);rgb(35, 54, 18);rgb(18, 54, 37);rgb(18, 35, 54);rgb(37, 18, 54);rgb(54, 18, 35)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(100 269.65)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 49);rgb(77, 53, 25);rgb(49, 77, 25);rgb(25, 77, 53);rgb(25, 49, 77);rgb(53, 25, 77);rgb(77, 25, 49)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 41);rgb(64, 44, 21);rgb(41, 64, 21);rgb(21, 64, 44);rgb(21, 41, 64);rgb(44, 21, 64);rgb(64, 21, 41)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 35);rgb(54, 37, 18);rgb(35, 54, 18);rgb(18, 54, 37);rgb(18, 35, 54);rgb(37, 18, 54);rgb(54, 18, 35)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(240 211.91)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 55);rgb(77, 47, 25);rgb(55, 77, 25);rgb(25, 77, 47);rgb(25, 55, 77);rgb(47, 25, 77);rgb(77, 25, 55)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 46);rgb(64, 39, 21);rgb(46, 64, 21);rgb(21, 64, 39);rgb(21, 46, 64);rgb(39, 21, 64);rgb(64, 21, 46)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 39);rgb(54, 33, 18);rgb(39, 54, 18);rgb(18, 54, 33);rgb(18, 39, 54);rgb(33, 18, 54);rgb(54, 18, 39)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(220 223.46)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 55);rgb(77, 47, 25);rgb(55, 77, 25);rgb(25, 77, 47);rgb(25, 55, 77);rgb(47, 25, 77);rgb(77, 25, 55)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 46);rgb(64, 39, 21);rgb(46, 64, 21);rgb(21, 64, 39);rgb(21, 46, 64);rgb(39, 21, 64);rgb(64, 21, 46)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 39);rgb(54, 33, 18);rgb(39, 54, 18);rgb(18, 54, 33);rgb(18, 39, 54);rgb(33, 18, 54);rgb(54, 18, 39)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(200 235.01)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 55);rgb(77, 47, 25);rgb(55, 77, 25);rgb(25, 77, 47);rgb(25, 55, 77);rgb(47, 25, 77);rgb(77, 25, 55)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 46);rgb(64, 39, 21);rgb(46, 64, 21);rgb(21, 64, 39);rgb(21, 46, 64);rgb(39, 21, 64);rgb(64, 21, 46)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 39);rgb(54, 33, 18);rgb(39, 54, 18);rgb(18, 54, 33);rgb(18, 39, 54);rgb(33, 18, 54);rgb(54, 18, 39)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(180 246.56)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 55);rgb(77, 47, 25);rgb(55, 77, 25);rgb(25, 77, 47);rgb(25, 55, 77);rgb(47, 25, 77);rgb(77, 25, 55)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 46);rgb(64, 39, 21);rgb(46, 64, 21);rgb(21, 64, 39);rgb(21, 46, 64);rgb(39, 21, 64);rgb(64, 21, 46)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 39);rgb(54, 33, 18);rgb(39, 54, 18);rgb(18, 54, 33);rgb(18, 39, 54);rgb(33, 18, 54);rgb(54, 18, 39)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(160 258.1)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 55);rgb(77, 47, 25);rgb(55, 77, 25);rgb(25, 77, 47);rgb(25, 55, 77);rgb(47, 25, 77);rgb(77, 25, 55)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 46);rgb(64, 39, 21);rgb(46, 64, 21);rgb(21, 64, 39);rgb(21, 46, 64);rgb(39, 21, 64);rgb(64, 21, 46)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 39);rgb(54, 33, 18);rgb(39, 54, 18);rgb(18, 54, 33);rgb(18, 39, 54);rgb(33, 18, 54);rgb(54, 18, 39)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(140 269.65)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 55);rgb(77, 47, 25);rgb(55, 77, 25);rgb(25, 77, 47);rgb(25, 55, 77);rgb(47, 25, 77);rgb(77, 25, 55)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 46);rgb(64, 39, 21);rgb(46, 64, 21);rgb(21, 64, 39);rgb(21, 46, 64);rgb(39, 21, 64);rgb(64, 21, 46)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 39);rgb(54, 33, 18);rgb(39, 54, 18);rgb(18, 54, 33);rgb(18, 39, 54);rgb(33, 18, 54);rgb(54, 18, 39)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(120 281.2)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 55);rgb(77, 47, 25);rgb(55, 77, 25);rgb(25, 77, 47);rgb(25, 55, 77);rgb(47, 25, 77);rgb(77, 25, 55)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 46);rgb(64, 39, 21);rgb(46, 64, 21);rgb(21, 64, 39);rgb(21, 46, 64);rgb(39, 21, 64);rgb(64, 21, 46)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 39);rgb(54, 33, 18);rgb(39, 54, 18);rgb(18, 54, 33);rgb(18, 39, 54);rgb(33, 18, 54);rgb(54, 18, 39)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(260 223.46)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 61);rgb(77, 41, 25);rgb(61, 77, 25);rgb(25, 77, 41);rgb(25, 61, 77);rgb(41, 25, 77);rgb(77, 25, 61)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 51);rgb(64, 34, 21);rgb(51, 64, 21);rgb(21, 64, 34);rgb(21, 51, 64);rgb(34, 21, 64);rgb(64, 21, 51)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 43);rgb(54, 29, 18);rgb(43, 54, 18);rgb(18, 54, 29);rgb(18, 43, 54);rgb(29, 18, 54);rgb(54, 18, 43)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(240 235.01)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 61);rgb(77, 41, 25);rgb(61, 77, 25);rgb(25, 77, 41);rgb(25, 61, 77);rgb(41, 25, 77);rgb(77, 25, 61)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 51);rgb(64, 34, 21);rgb(51, 64, 21);rgb(21, 64, 34);rgb(21, 51, 64);rgb(34, 21, 64);rgb(64, 21, 51)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 43);rgb(54, 29, 18);rgb(43, 54, 18);rgb(18, 54, 29);rgb(18, 43, 54);rgb(29, 18, 54);rgb(54, 18, 43)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(220 246.56)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 61);rgb(77, 41, 25);rgb(61, 77, 25);rgb(25, 77, 41);rgb(25, 61, 77);rgb(41, 25, 77);rgb(77, 25, 61)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 51);rgb(64, 34, 21);rgb(51, 64, 21);rgb(21, 64, 34);rgb(21, 51, 64);rgb(34, 21, 64);rgb(64, 21, 51)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 43);rgb(54, 29, 18);rgb(43, 54, 18);rgb(18, 54, 29);rgb(18, 43, 54);rgb(29, 18, 54);rgb(54, 18, 43)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(200 258.1)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 61);rgb(77, 41, 25);rgb(61, 77, 25);rgb(25, 77, 41);rgb(25, 61, 77);rgb(41, 25, 77);rgb(77, 25, 61)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 51);rgb(64, 34, 21);rgb(51, 64, 21);rgb(21, 64, 34);rgb(21, 51, 64);rgb(34, 21, 64);rgb(64, 21, 51)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 43);rgb(54, 29, 18);rgb(43, 54, 18);rgb(18, 54, 29);rgb(18, 43, 54);rgb(29, 18, 54);rgb(54, 18, 43)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(180 269.65)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 61);rgb(77, 41, 25);rgb(61, 77, 25);rgb(25, 77, 41);rgb(25, 61, 77);rgb(41, 25, 77);rgb(77, 25, 61)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 51);rgb(64, 34, 21);rgb(51, 64, 21);rgb(21, 64, 34);rgb(21, 51, 64);rgb(34, 21, 64);rgb(64, 21, 51)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 43);rgb(54, 29, 18);rgb(43, 54, 18);rgb(18, 54, 29);rgb(18, 43, 54);rgb(29, 18, 54);rgb(54, 18, 43)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(160 281.2)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 61);rgb(77, 41, 25);rgb(61, 77, 25);rgb(25, 77, 41);rgb(25, 61, 77);rgb(41, 25, 77);rgb(77, 25, 61)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 51);rgb(64, 34, 21);rgb(51, 64, 21);rgb(21, 64, 34);rgb(21, 51, 64);rgb(34, 21, 64);rgb(64, 21, 51)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 43);rgb(54, 29, 18);rgb(43, 54, 18);rgb(18, 54, 29);rgb(18, 43, 54);rgb(29, 18, 54);rgb(54, 18, 43)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(140 292.74)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 61);rgb(77, 41, 25);rgb(61, 77, 25);rgb(25, 77, 41);rgb(25, 61, 77);rgb(41, 25, 77);rgb(77, 25, 61)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 51);rgb(64, 34, 21);rgb(51, 64, 21);rgb(21, 64, 34);rgb(21, 51, 64);rgb(34, 21, 64);rgb(64, 21, 51)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 43);rgb(54, 29, 18);rgb(43, 54, 18);rgb(18, 54, 29);rgb(18, 43, 54);rgb(29, 18, 54);rgb(54, 18, 43)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(280 235.01)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 67);rgb(77, 35, 25);rgb(67, 77, 25);rgb(25, 77, 35);rgb(25, 67, 77);rgb(35, 25, 77);rgb(77, 25, 67)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 56);rgb(64, 29, 21);rgb(56, 64, 21);rgb(21, 64, 29);rgb(21, 56, 64);rgb(29, 21, 64);rgb(64, 21, 56)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 47);rgb(54, 24, 18);rgb(47, 54, 18);rgb(18, 54, 24);rgb(18, 47, 54);rgb(24, 18, 54);rgb(54, 18, 47)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(260 246.56)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 67);rgb(77, 35, 25);rgb(67, 77, 25);rgb(25, 77, 35);rgb(25, 67, 77);rgb(35, 25, 77);rgb(77, 25, 67)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 56);rgb(64, 29, 21);rgb(56, 64, 21);rgb(21, 64, 29);rgb(21, 56, 64);rgb(29, 21, 64);rgb(64, 21, 56)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 47);rgb(54, 24, 18);rgb(47, 54, 18);rgb(18, 54, 24);rgb(18, 47, 54);rgb(24, 18, 54);rgb(54, 18, 47)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(240 258.1)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 67);rgb(77, 35, 25);rgb(67, 77, 25);rgb(25, 77, 35);rgb(25, 67, 77);rgb(35, 25, 77);rgb(77, 25, 67)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 56);rgb(64, 29, 21);rgb(56, 64, 21);rgb(21, 64, 29);rgb(21, 56, 64);rgb(29, 21, 64);rgb(64, 21, 56)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 47);rgb(54, 24, 18);rgb(47, 54, 18);rgb(18, 54, 24);rgb(18, 47, 54);rgb(24, 18, 54);rgb(54, 18, 47)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(220 269.65)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 67);rgb(77, 35, 25);rgb(67, 77, 25);rgb(25, 77, 35);rgb(25, 67, 77);rgb(35, 25, 77);rgb(77, 25, 67)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 56);rgb(64, 29, 21);rgb(56, 64, 21);rgb(21, 64, 29);rgb(21, 56, 64);rgb(29, 21, 64);rgb(64, 21, 56)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 47);rgb(54, 24, 18);rgb(47, 54, 18);rgb(18, 54, 24);rgb(18, 47, 54);rgb(24, 18, 54);rgb(54, 18, 47)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(200 281.2)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 67);rgb(77, 35, 25);rgb(67, 77, 25);rgb(25, 77, 35);rgb(25, 67, 77);rgb(35, 25, 77);rgb(77, 25, 67)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 56);rgb(64, 29, 21);rgb(56, 64, 21);rgb(21, 64, 29);rgb(21, 56, 64);rgb(29, 21, 64);rgb(64, 21, 56)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 47);rgb(54, 24, 18);rgb(47, 54, 18);rgb(18, 54, 24);rgb(18, 47, 54);rgb(24, 18, 54);rgb(54, 18, 47)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(180 292.74)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 67);rgb(77, 35, 25);rgb(67, 77, 25);rgb(25, 77, 35);rgb(25, 67, 77);rgb(35, 25, 77);rgb(77, 25, 67)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 56);rgb(64, 29, 21);rgb(56, 64, 21);rgb(21, 64, 29);rgb(21, 56, 64);rgb(29, 21, 64);rgb(64, 21, 56)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 47);rgb(54, 24, 18);rgb(47, 54, 18);rgb(18, 54, 24);rgb(18, 47, 54);rgb(24, 18, 54);rgb(54, 18, 47)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(160 304.29)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 67);rgb(77, 35, 25);rgb(67, 77, 25);rgb(25, 77, 35);rgb(25, 67, 77);rgb(35, 25, 77);rgb(77, 25, 67)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 56);rgb(64, 29, 21);rgb(56, 64, 21);rgb(21, 64, 29);rgb(21, 56, 64);rgb(29, 21, 64);rgb(64, 21, 56)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 47);rgb(54, 24, 18);rgb(47, 54, 18);rgb(18, 54, 24);rgb(18, 47, 54);rgb(24, 18, 54);rgb(54, 18, 47)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(300 246.56)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 73);rgb(77, 29, 25);rgb(73, 77, 25);rgb(25, 77, 29);rgb(25, 73, 77);rgb(29, 25, 77);rgb(77, 25, 73)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 61);rgb(64, 24, 21);rgb(61, 64, 21);rgb(21, 64, 24);rgb(21, 61, 64);rgb(24, 21, 64);rgb(64, 21, 61)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 51);rgb(54, 20, 18);rgb(51, 54, 18);rgb(18, 54, 20);rgb(18, 51, 54);rgb(20, 18, 54);rgb(54, 18, 51)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(280 258.1)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 73);rgb(77, 29, 25);rgb(73, 77, 25);rgb(25, 77, 29);rgb(25, 73, 77);rgb(29, 25, 77);rgb(77, 25, 73)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 61);rgb(64, 24, 21);rgb(61, 64, 21);rgb(21, 64, 24);rgb(21, 61, 64);rgb(24, 21, 64);rgb(64, 21, 61)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 51);rgb(54, 20, 18);rgb(51, 54, 18);rgb(18, 54, 20);rgb(18, 51, 54);rgb(20, 18, 54);rgb(54, 18, 51)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(260 269.65)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 73);rgb(77, 29, 25);rgb(73, 77, 25);rgb(25, 77, 29);rgb(25, 73, 77);rgb(29, 25, 77);rgb(77, 25, 73)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 61);rgb(64, 24, 21);rgb(61, 64, 21);rgb(21, 64, 24);rgb(21, 61, 64);rgb(24, 21, 64);rgb(64, 21, 61)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 51);rgb(54, 20, 18);rgb(51, 54, 18);rgb(18, 54, 20);rgb(18, 51, 54);rgb(20, 18, 54);rgb(54, 18, 51)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(240 281.2)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 73);rgb(77, 29, 25);rgb(73, 77, 25);rgb(25, 77, 29);rgb(25, 73, 77);rgb(29, 25, 77);rgb(77, 25, 73)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 61);rgb(64, 24, 21);rgb(61, 64, 21);rgb(21, 64, 24);rgb(21, 61, 64);rgb(24, 21, 64);rgb(64, 21, 61)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 51);rgb(54, 20, 18);rgb(51, 54, 18);rgb(18, 54, 20);rgb(18, 51, 54);rgb(20, 18, 54);rgb(54, 18, 51)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(220 292.74)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 73);rgb(77, 29, 25);rgb(73, 77, 25);rgb(25, 77, 29);rgb(25, 73, 77);rgb(29, 25, 77);rgb(77, 25, 73)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 61);rgb(64, 24, 21);rgb(61, 64, 21);rgb(21, 64, 24);rgb(21, 61, 64);rgb(24, 21, 64);rgb(64, 21, 61)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 51);rgb(54, 20, 18);rgb(51, 54, 18);rgb(18, 54, 20);rgb(18, 51, 54);rgb(20, 18, 54);rgb(54, 18, 51)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(200 304.29)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 73);rgb(77, 29, 25);rgb(73, 77, 25);rgb(25, 77, 29);rgb(25, 73, 77);rgb(29, 25, 77);rgb(77, 25, 73)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 61);rgb(64, 24, 21);rgb(61, 64, 21);rgb(21, 64, 24);rgb(21, 61, 64);rgb(24, 21, 64);rgb(64, 21, 61)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 51);rgb(54, 20, 18);rgb(51, 54, 18);rgb(18, 54, 20);rgb(18, 51, 54);rgb(20, 18, 54);rgb(54, 18, 51)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(180 315.84)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 25, 73);rgb(77, 29, 25);rgb(73, 77, 25);rgb(25, 77, 29);rgb(25, 73, 77);rgb(29, 25, 77);rgb(77, 25, 73)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 21, 61);rgb(64, 24, 21);rgb(61, 64, 21);rgb(21, 64, 24);rgb(21, 61, 64);rgb(24, 21, 64);rgb(64, 21, 61)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 18, 51);rgb(54, 20, 18);rgb(51, 54, 18);rgb(18, 54, 20);rgb(18, 51, 54);rgb(20, 18, 54);rgb(54, 18, 51)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(320 258.1)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(74, 25, 77);rgb(77, 25, 28);rgb(77, 74, 25);rgb(28, 77, 25);rgb(25, 77, 74);rgb(25, 28, 77);rgb(74, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(62, 21, 64);rgb(64, 21, 23);rgb(64, 62, 21);rgb(23, 64, 21);rgb(21, 64, 62);rgb(21, 23, 64);rgb(62, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(52, 18, 54);rgb(54, 18, 20);rgb(54, 52, 18);rgb(20, 54, 18);rgb(18, 54, 52);rgb(18, 20, 54);rgb(52, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(300 269.65)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(74, 25, 77);rgb(77, 25, 28);rgb(77, 74, 25);rgb(28, 77, 25);rgb(25, 77, 74);rgb(25, 28, 77);rgb(74, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(62, 21, 64);rgb(64, 21, 23);rgb(64, 62, 21);rgb(23, 64, 21);rgb(21, 64, 62);rgb(21, 23, 64);rgb(62, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(52, 18, 54);rgb(54, 18, 20);rgb(54, 52, 18);rgb(20, 54, 18);rgb(18, 54, 52);rgb(18, 20, 54);rgb(52, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(280 281.2)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(74, 25, 77);rgb(77, 25, 28);rgb(77, 74, 25);rgb(28, 77, 25);rgb(25, 77, 74);rgb(25, 28, 77);rgb(74, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(62, 21, 64);rgb(64, 21, 23);rgb(64, 62, 21);rgb(23, 64, 21);rgb(21, 64, 62);rgb(21, 23, 64);rgb(62, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(52, 18, 54);rgb(54, 18, 20);rgb(54, 52, 18);rgb(20, 54, 18);rgb(18, 54, 52);rgb(18, 20, 54);rgb(52, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(260 292.74)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(74, 25, 77);rgb(77, 25, 28);rgb(77, 74, 25);rgb(28, 77, 25);rgb(25, 77, 74);rgb(25, 28, 77);rgb(74, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(62, 21, 64);rgb(64, 21, 23);rgb(64, 62, 21);rgb(23, 64, 21);rgb(21, 64, 62);rgb(21, 23, 64);rgb(62, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(52, 18, 54);rgb(54, 18, 20);rgb(54, 52, 18);rgb(20, 54, 18);rgb(18, 54, 52);rgb(18, 20, 54);rgb(52, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(240 304.29)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(74, 25, 77);rgb(77, 25, 28);rgb(77, 74, 25);rgb(28, 77, 25);rgb(25, 77, 74);rgb(25, 28, 77);rgb(74, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(62, 21, 64);rgb(64, 21, 23);rgb(64, 62, 21);rgb(23, 64, 21);rgb(21, 64, 62);rgb(21, 23, 64);rgb(62, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(52, 18, 54);rgb(54, 18, 20);rgb(54, 52, 18);rgb(20, 54, 18);rgb(18, 54, 52);rgb(18, 20, 54);rgb(52, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(220 315.84)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(74, 25, 77);rgb(77, 25, 28);rgb(77, 74, 25);rgb(28, 77, 25);rgb(25, 77, 74);rgb(25, 28, 77);rgb(74, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(62, 21, 64);rgb(64, 21, 23);rgb(64, 62, 21);rgb(23, 64, 21);rgb(21, 64, 62);rgb(21, 23, 64);rgb(62, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(52, 18, 54);rgb(54, 18, 20);rgb(54, 52, 18);rgb(20, 54, 18);rgb(18, 54, 52);rgb(18, 20, 54);rgb(52, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(200 327.38)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(74, 25, 77);rgb(77, 25, 28);rgb(77, 74, 25);rgb(28, 77, 25);rgb(25, 77, 74);rgb(25, 28, 77);rgb(74, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(62, 21, 64);rgb(64, 21, 23);rgb(64, 62, 21);rgb(23, 64, 21);rgb(21, 64, 62);rgb(21, 23, 64);rgb(62, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(52, 18, 54);rgb(54, 18, 20);rgb(54, 52, 18);rgb(20, 54, 18);rgb(18, 54, 52);rgb(18, 20, 54);rgb(52, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(340 269.65)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(68, 25, 77);rgb(77, 25, 34);rgb(77, 68, 25);rgb(34, 77, 25);rgb(25, 77, 68);rgb(25, 34, 77);rgb(68, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(57, 21, 64);rgb(64, 21, 28);rgb(64, 57, 21);rgb(28, 64, 21);rgb(21, 64, 57);rgb(21, 28, 64);rgb(57, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(48, 18, 54);rgb(54, 18, 24);rgb(54, 48, 18);rgb(24, 54, 18);rgb(18, 54, 48);rgb(18, 24, 54);rgb(48, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(320 281.2)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(68, 25, 77);rgb(77, 25, 34);rgb(77, 68, 25);rgb(34, 77, 25);rgb(25, 77, 68);rgb(25, 34, 77);rgb(68, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(57, 21, 64);rgb(64, 21, 28);rgb(64, 57, 21);rgb(28, 64, 21);rgb(21, 64, 57);rgb(21, 28, 64);rgb(57, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(48, 18, 54);rgb(54, 18, 24);rgb(54, 48, 18);rgb(24, 54, 18);rgb(18, 54, 48);rgb(18, 24, 54);rgb(48, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(300 292.74)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(68, 25, 77);rgb(77, 25, 34);rgb(77, 68, 25);rgb(34, 77, 25);rgb(25, 77, 68);rgb(25, 34, 77);rgb(68, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(57, 21, 64);rgb(64, 21, 28);rgb(64, 57, 21);rgb(28, 64, 21);rgb(21, 64, 57);rgb(21, 28, 64);rgb(57, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(48, 18, 54);rgb(54, 18, 24);rgb(54, 48, 18);rgb(24, 54, 18);rgb(18, 54, 48);rgb(18, 24, 54);rgb(48, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(280 304.29)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(68, 25, 77);rgb(77, 25, 34);rgb(77, 68, 25);rgb(34, 77, 25);rgb(25, 77, 68);rgb(25, 34, 77);rgb(68, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(57, 21, 64);rgb(64, 21, 28);rgb(64, 57, 21);rgb(28, 64, 21);rgb(21, 64, 57);rgb(21, 28, 64);rgb(57, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(48, 18, 54);rgb(54, 18, 24);rgb(54, 48, 18);rgb(24, 54, 18);rgb(18, 54, 48);rgb(18, 24, 54);rgb(48, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(260 315.84)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(68, 25, 77);rgb(77, 25, 34);rgb(77, 68, 25);rgb(34, 77, 25);rgb(25, 77, 68);rgb(25, 34, 77);rgb(68, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(57, 21, 64);rgb(64, 21, 28);rgb(64, 57, 21);rgb(28, 64, 21);rgb(21, 64, 57);rgb(21, 28, 64);rgb(57, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(48, 18, 54);rgb(54, 18, 24);rgb(54, 48, 18);rgb(24, 54, 18);rgb(18, 54, 48);rgb(18, 24, 54);rgb(48, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(240 327.38)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(68, 25, 77);rgb(77, 25, 34);rgb(77, 68, 25);rgb(34, 77, 25);rgb(25, 77, 68);rgb(25, 34, 77);rgb(68, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(57, 21, 64);rgb(64, 21, 28);rgb(64, 57, 21);rgb(28, 64, 21);rgb(21, 64, 57);rgb(21, 28, 64);rgb(57, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(48, 18, 54);rgb(54, 18, 24);rgb(54, 48, 18);rgb(24, 54, 18);rgb(18, 54, 48);rgb(18, 24, 54);rgb(48, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(220 338.93)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(68, 25, 77);rgb(77, 25, 34);rgb(77, 68, 25);rgb(34, 77, 25);rgb(25, 77, 68);rgb(25, 34, 77);rgb(68, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(57, 21, 64);rgb(64, 21, 28);rgb(64, 57, 21);rgb(28, 64, 21);rgb(21, 64, 57);rgb(21, 28, 64);rgb(57, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(48, 18, 54);rgb(54, 18, 24);rgb(54, 48, 18);rgb(24, 54, 18);rgb(18, 54, 48);rgb(18, 24, 54);rgb(48, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(360 281.2)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(62, 25, 77);rgb(77, 25, 40);rgb(77, 62, 25);rgb(40, 77, 25);rgb(25, 77, 62);rgb(25, 40, 77);rgb(62, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(52, 21, 64);rgb(64, 21, 33);rgb(64, 52, 21);rgb(33, 64, 21);rgb(21, 64, 52);rgb(21, 33, 64);rgb(52, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(43, 18, 54);rgb(54, 18, 28);rgb(54, 43, 18);rgb(28, 54, 18);rgb(18, 54, 43);rgb(18, 28, 54);rgb(43, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(340 292.74)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(62, 25, 77);rgb(77, 25, 40);rgb(77, 62, 25);rgb(40, 77, 25);rgb(25, 77, 62);rgb(25, 40, 77);rgb(62, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(52, 21, 64);rgb(64, 21, 33);rgb(64, 52, 21);rgb(33, 64, 21);rgb(21, 64, 52);rgb(21, 33, 64);rgb(52, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(43, 18, 54);rgb(54, 18, 28);rgb(54, 43, 18);rgb(28, 54, 18);rgb(18, 54, 43);rgb(18, 28, 54);rgb(43, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(320 304.29)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(62, 25, 77);rgb(77, 25, 40);rgb(77, 62, 25);rgb(40, 77, 25);rgb(25, 77, 62);rgb(25, 40, 77);rgb(62, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(52, 21, 64);rgb(64, 21, 33);rgb(64, 52, 21);rgb(33, 64, 21);rgb(21, 64, 52);rgb(21, 33, 64);rgb(52, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(43, 18, 54);rgb(54, 18, 28);rgb(54, 43, 18);rgb(28, 54, 18);rgb(18, 54, 43);rgb(18, 28, 54);rgb(43, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(300 315.84)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(62, 25, 77);rgb(77, 25, 40);rgb(77, 62, 25);rgb(40, 77, 25);rgb(25, 77, 62);rgb(25, 40, 77);rgb(62, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(52, 21, 64);rgb(64, 21, 33);rgb(64, 52, 21);rgb(33, 64, 21);rgb(21, 64, 52);rgb(21, 33, 64);rgb(52, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(43, 18, 54);rgb(54, 18, 28);rgb(54, 43, 18);rgb(28, 54, 18);rgb(18, 54, 43);rgb(18, 28, 54);rgb(43, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(280 327.38)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(62, 25, 77);rgb(77, 25, 40);rgb(77, 62, 25);rgb(40, 77, 25);rgb(25, 77, 62);rgb(25, 40, 77);rgb(62, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(52, 21, 64);rgb(64, 21, 33);rgb(64, 52, 21);rgb(33, 64, 21);rgb(21, 64, 52);rgb(21, 33, 64);rgb(52, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(43, 18, 54);rgb(54, 18, 28);rgb(54, 43, 18);rgb(28, 54, 18);rgb(18, 54, 43);rgb(18, 28, 54);rgb(43, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(260 338.93)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(62, 25, 77);rgb(77, 25, 40);rgb(77, 62, 25);rgb(40, 77, 25);rgb(25, 77, 62);rgb(25, 40, 77);rgb(62, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(52, 21, 64);rgb(64, 21, 33);rgb(64, 52, 21);rgb(33, 64, 21);rgb(21, 64, 52);rgb(21, 33, 64);rgb(52, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(43, 18, 54);rgb(54, 18, 28);rgb(54, 43, 18);rgb(28, 54, 18);rgb(18, 54, 43);rgb(18, 28, 54);rgb(43, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(240 350.48)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(62, 25, 77);rgb(77, 25, 40);rgb(77, 62, 25);rgb(40, 77, 25);rgb(25, 77, 62);rgb(25, 40, 77);rgb(62, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(52, 21, 64);rgb(64, 21, 33);rgb(64, 52, 21);rgb(33, 64, 21);rgb(21, 64, 52);rgb(21, 33, 64);rgb(52, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(43, 18, 54);rgb(54, 18, 28);rgb(54, 43, 18);rgb(28, 54, 18);rgb(18, 54, 43);rgb(18, 28, 54);rgb(43, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(380 292.74)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(56, 25, 77);rgb(77, 25, 46);rgb(77, 56, 25);rgb(46, 77, 25);rgb(25, 77, 56);rgb(25, 46, 77);rgb(56, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(47, 21, 64);rgb(64, 21, 38);rgb(64, 47, 21);rgb(38, 64, 21);rgb(21, 64, 47);rgb(21, 38, 64);rgb(47, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(39, 18, 54);rgb(54, 18, 32);rgb(54, 39, 18);rgb(32, 54, 18);rgb(18, 54, 39);rgb(18, 32, 54);rgb(39, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(360 304.29)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(56, 25, 77);rgb(77, 25, 46);rgb(77, 56, 25);rgb(46, 77, 25);rgb(25, 77, 56);rgb(25, 46, 77);rgb(56, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(47, 21, 64);rgb(64, 21, 38);rgb(64, 47, 21);rgb(38, 64, 21);rgb(21, 64, 47);rgb(21, 38, 64);rgb(47, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(39, 18, 54);rgb(54, 18, 32);rgb(54, 39, 18);rgb(32, 54, 18);rgb(18, 54, 39);rgb(18, 32, 54);rgb(39, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(340 315.84)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(56, 25, 77);rgb(77, 25, 46);rgb(77, 56, 25);rgb(46, 77, 25);rgb(25, 77, 56);rgb(25, 46, 77);rgb(56, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(47, 21, 64);rgb(64, 21, 38);rgb(64, 47, 21);rgb(38, 64, 21);rgb(21, 64, 47);rgb(21, 38, 64);rgb(47, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(39, 18, 54);rgb(54, 18, 32);rgb(54, 39, 18);rgb(32, 54, 18);rgb(18, 54, 39);rgb(18, 32, 54);rgb(39, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(320 327.38)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(56, 25, 77);rgb(77, 25, 46);rgb(77, 56, 25);rgb(46, 77, 25);rgb(25, 77, 56);rgb(25, 46, 77);rgb(56, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(47, 21, 64);rgb(64, 21, 38);rgb(64, 47, 21);rgb(38, 64, 21);rgb(21, 64, 47);rgb(21, 38, 64);rgb(47, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(39, 18, 54);rgb(54, 18, 32);rgb(54, 39, 18);rgb(32, 54, 18);rgb(18, 54, 39);rgb(18, 32, 54);rgb(39, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(300 338.93)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(56, 25, 77);rgb(77, 25, 46);rgb(77, 56, 25);rgb(46, 77, 25);rgb(25, 77, 56);rgb(25, 46, 77);rgb(56, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(47, 21, 64);rgb(64, 21, 38);rgb(64, 47, 21);rgb(38, 64, 21);rgb(21, 64, 47);rgb(21, 38, 64);rgb(47, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(39, 18, 54);rgb(54, 18, 32);rgb(54, 39, 18);rgb(32, 54, 18);rgb(18, 54, 39);rgb(18, 32, 54);rgb(39, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(280 350.48)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(56, 25, 77);rgb(77, 25, 46);rgb(77, 56, 25);rgb(46, 77, 25);rgb(25, 77, 56);rgb(25, 46, 77);rgb(56, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(47, 21, 64);rgb(64, 21, 38);rgb(64, 47, 21);rgb(38, 64, 21);rgb(21, 64, 47);rgb(21, 38, 64);rgb(47, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(39, 18, 54);rgb(54, 18, 32);rgb(54, 39, 18);rgb(32, 54, 18);rgb(18, 54, 39);rgb(18, 32, 54);rgb(39, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(260 362.03)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(56, 25, 77);rgb(77, 25, 46);rgb(77, 56, 25);rgb(46, 77, 25);rgb(25, 77, 56);rgb(25, 46, 77);rgb(56, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(47, 21, 64);rgb(64, 21, 38);rgb(64, 47, 21);rgb(38, 64, 21);rgb(21, 64, 47);rgb(21, 38, 64);rgb(47, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(39, 18, 54);rgb(54, 18, 32);rgb(54, 39, 18);rgb(32, 54, 18);rgb(18, 54, 39);rgb(18, 32, 54);rgb(39, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(400 304.29)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(50, 25, 77);rgb(77, 25, 52);rgb(77, 50, 25);rgb(52, 77, 25);rgb(25, 77, 50);rgb(25, 52, 77);rgb(50, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(42, 21, 64);rgb(64, 21, 43);rgb(64, 42, 21);rgb(43, 64, 21);rgb(21, 64, 42);rgb(21, 43, 64);rgb(42, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(35, 18, 54);rgb(54, 18, 36);rgb(54, 35, 18);rgb(36, 54, 18);rgb(18, 54, 35);rgb(18, 36, 54);rgb(35, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(380 315.84)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(50, 25, 77);rgb(77, 25, 52);rgb(77, 50, 25);rgb(52, 77, 25);rgb(25, 77, 50);rgb(25, 52, 77);rgb(50, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(42, 21, 64);rgb(64, 21, 43);rgb(64, 42, 21);rgb(43, 64, 21);rgb(21, 64, 42);rgb(21, 43, 64);rgb(42, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(35, 18, 54);rgb(54, 18, 36);rgb(54, 35, 18);rgb(36, 54, 18);rgb(18, 54, 35);rgb(18, 36, 54);rgb(35, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(360 327.38)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(50, 25, 77);rgb(77, 25, 52);rgb(77, 50, 25);rgb(52, 77, 25);rgb(25, 77, 50);rgb(25, 52, 77);rgb(50, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(42, 21, 64);rgb(64, 21, 43);rgb(64, 42, 21);rgb(43, 64, 21);rgb(21, 64, 42);rgb(21, 43, 64);rgb(42, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(35, 18, 54);rgb(54, 18, 36);rgb(54, 35, 18);rgb(36, 54, 18);rgb(18, 54, 35);rgb(18, 36, 54);rgb(35, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(340 338.93)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(50, 25, 77);rgb(77, 25, 52);rgb(77, 50, 25);rgb(52, 77, 25);rgb(25, 77, 50);rgb(25, 52, 77);rgb(50, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(42, 21, 64);rgb(64, 21, 43);rgb(64, 42, 21);rgb(43, 64, 21);rgb(21, 64, 42);rgb(21, 43, 64);rgb(42, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(35, 18, 54);rgb(54, 18, 36);rgb(54, 35, 18);rgb(36, 54, 18);rgb(18, 54, 35);rgb(18, 36, 54);rgb(35, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(320 350.48)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(50, 25, 77);rgb(77, 25, 52);rgb(77, 50, 25);rgb(52, 77, 25);rgb(25, 77, 50);rgb(25, 52, 77);rgb(50, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(42, 21, 64);rgb(64, 21, 43);rgb(64, 42, 21);rgb(43, 64, 21);rgb(21, 64, 42);rgb(21, 43, 64);rgb(42, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(35, 18, 54);rgb(54, 18, 36);rgb(54, 35, 18);rgb(36, 54, 18);rgb(18, 54, 35);rgb(18, 36, 54);rgb(35, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(300 362.03)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(50, 25, 77);rgb(77, 25, 52);rgb(77, 50, 25);rgb(52, 77, 25);rgb(25, 77, 50);rgb(25, 52, 77);rgb(50, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(42, 21, 64);rgb(64, 21, 43);rgb(64, 42, 21);rgb(43, 64, 21);rgb(21, 64, 42);rgb(21, 43, 64);rgb(42, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(35, 18, 54);rgb(54, 18, 36);rgb(54, 35, 18);rgb(36, 54, 18);rgb(18, 54, 35);rgb(18, 36, 54);rgb(35, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(280 370.52)"><animateTransform attributeName="transform" type="translate" values="280 373.57;280 370.52" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(75, 38, 115);rgb(115, 38, 78);rgb(115, 75, 38);rgb(78, 115, 38);rgb(38, 115, 75);rgb(38, 78, 115);rgb(75, 38, 115)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="5.24" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(63, 32, 96);rgb(96, 32, 65);rgb(96, 63, 32);rgb(65, 96, 32);rgb(32, 96, 63);rgb(32, 65, 96);rgb(63, 32, 96)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;5.24" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="5.24" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(53, 27, 80);rgb(80, 27, 54);rgb(80, 53, 27);rgb(54, 80, 27);rgb(27, 80, 53);rgb(27, 54, 80);rgb(53, 27, 80)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;5.24" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(420 282.65)"><animateTransform attributeName="transform" type="translate" values="420 315.84;420 282.65" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(88, 51, 153);rgb(153, 51, 116);rgb(153, 88, 51);rgb(116, 153, 51);rgb(51, 153, 88);rgb(51, 116, 153);rgb(88, 51, 153)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="31.34" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(74, 43, 128);rgb(128, 43, 97);rgb(128, 74, 43);rgb(97, 128, 43);rgb(43, 128, 74);rgb(43, 97, 128);rgb(74, 43, 128)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;31.34" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="31.34" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(62, 36, 107);rgb(107, 36, 81);rgb(107, 62, 36);rgb(81, 107, 36);rgb(36, 107, 62);rgb(36, 81, 107);rgb(62, 36, 107)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;31.34" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(400 324.33)"><animateTransform attributeName="transform" type="translate" values="400 327.38;400 324.33" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(66, 38, 115);rgb(115, 38, 87);rgb(115, 66, 38);rgb(87, 115, 38);rgb(38, 115, 66);rgb(38, 87, 115);rgb(66, 38, 115)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="5.24" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(55, 32, 96);rgb(96, 32, 73);rgb(96, 55, 32);rgb(73, 96, 32);rgb(32, 96, 55);rgb(32, 73, 96);rgb(55, 32, 96)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;5.24" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="5.24" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(46, 27, 80);rgb(80, 27, 61);rgb(80, 46, 27);rgb(61, 80, 27);rgb(27, 80, 46);rgb(27, 61, 80);rgb(46, 27, 80)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;5.24" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(380 338.93)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(44, 25, 77);rgb(77, 25, 58);rgb(77, 44, 25);rgb(58, 77, 25);rgb(25, 77, 44);rgb(25, 58, 77);rgb(44, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(37, 21, 64);rgb(64, 21, 48);rgb(64, 37, 21);rgb(48, 64, 21);rgb(21, 64, 37);rgb(21, 48, 64);rgb(37, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(31, 18, 54);rgb(54, 18, 40);rgb(54, 31, 18);rgb(40, 54, 18);rgb(18, 54, 31);rgb(18, 40, 54);rgb(31, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(360 323.07)"><animateTransform attributeName="transform" type="translate" values="360 350.48;360 323.07" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 45, 134);rgb(134, 45, 101);rgb(134, 77, 45);rgb(101, 134, 45);rgb(45, 134, 77);rgb(45, 101, 134);rgb(77, 45, 134)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="26.33" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(65, 37, 112);rgb(112, 37, 85);rgb(112, 65, 37);rgb(85, 112, 37);rgb(37, 112, 65);rgb(37, 85, 112);rgb(65, 37, 112)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;26.33" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="26.33" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 31, 94);rgb(94, 31, 71);rgb(94, 54, 31);rgb(71, 94, 31);rgb(31, 94, 54);rgb(31, 71, 94);rgb(54, 31, 94)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;26.33" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(340 327.03)"><animateTransform attributeName="transform" type="translate" values="340 362.03;340 327.03" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(88, 51, 153);rgb(153, 51, 116);rgb(153, 88, 51);rgb(116, 153, 51);rgb(51, 153, 88);rgb(51, 116, 153);rgb(88, 51, 153)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="32.91" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(74, 43, 128);rgb(128, 43, 97);rgb(128, 74, 43);rgb(97, 128, 43);rgb(43, 128, 74);rgb(43, 97, 128);rgb(74, 43, 128)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;32.91" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="32.91" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(62, 36, 107);rgb(107, 36, 81);rgb(107, 62, 36);rgb(81, 107, 36);rgb(36, 107, 62);rgb(36, 81, 107);rgb(62, 36, 107)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;32.91" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(320 346.17)"><animateTransform attributeName="transform" type="translate" values="320 373.57;320 346.17" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 45, 134);rgb(134, 45, 101);rgb(134, 77, 45);rgb(101, 134, 45);rgb(45, 134, 77);rgb(45, 101, 134);rgb(77, 45, 134)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="26.33" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(65, 37, 112);rgb(112, 37, 85);rgb(112, 65, 37);rgb(85, 112, 37);rgb(37, 112, 65);rgb(37, 85, 112);rgb(65, 37, 112)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;26.33" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="26.33" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 31, 94);rgb(94, 31, 71);rgb(94, 54, 31);rgb(71, 94, 31);rgb(31, 94, 54);rgb(31, 71, 94);rgb(54, 31, 94)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;26.33" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(300 376.38)"><animateTransform attributeName="transform" type="translate" values="300 385.12;300 376.38" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(66, 38, 115);rgb(115, 38, 87);rgb(115, 66, 38);rgb(87, 115, 38);rgb(38, 115, 66);rgb(38, 87, 115);rgb(66, 38, 115)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="10.17" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(55, 32, 96);rgb(96, 32, 73);rgb(96, 55, 32);rgb(73, 96, 32);rgb(32, 96, 55);rgb(32, 73, 96);rgb(55, 32, 96)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;10.17" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="10.17" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(46, 27, 80);rgb(80, 27, 61);rgb(80, 46, 27);rgb(61, 80, 27);rgb(27, 80, 46);rgb(27, 61, 80);rgb(46, 27, 80)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;10.17" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(440 327.38)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(38, 25, 77);rgb(77, 25, 64);rgb(77, 38, 25);rgb(64, 77, 25);rgb(25, 77, 38);rgb(25, 64, 77);rgb(38, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(32, 21, 64);rgb(64, 21, 53);rgb(64, 32, 21);rgb(53, 64, 21);rgb(21, 64, 32);rgb(21, 53, 64);rgb(32, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(27, 18, 54);rgb(54, 18, 45);rgb(54, 27, 18);rgb(45, 54, 18);rgb(18, 54, 27);rgb(18, 45, 54);rgb(27, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(420 338.93)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(38, 25, 77);rgb(77, 25, 64);rgb(77, 38, 25);rgb(64, 77, 25);rgb(25, 77, 38);rgb(25, 64, 77);rgb(38, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(32, 21, 64);rgb(64, 21, 53);rgb(64, 32, 21);rgb(53, 64, 21);rgb(21, 64, 32);rgb(21, 53, 64);rgb(32, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(27, 18, 54);rgb(54, 18, 45);rgb(54, 27, 18);rgb(45, 54, 18);rgb(18, 54, 27);rgb(18, 45, 54);rgb(27, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(400 350.48)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(38, 25, 77);rgb(77, 25, 64);rgb(77, 38, 25);rgb(64, 77, 25);rgb(25, 77, 38);rgb(25, 64, 77);rgb(38, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(32, 21, 64);rgb(64, 21, 53);rgb(64, 32, 21);rgb(53, 64, 21);rgb(21, 64, 32);rgb(21, 53, 64);rgb(32, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(27, 18, 54);rgb(54, 18, 45);rgb(54, 27, 18);rgb(45, 54, 18);rgb(18, 54, 27);rgb(18, 45, 54);rgb(27, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(380 362.03)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(38, 25, 77);rgb(77, 25, 64);rgb(77, 38, 25);rgb(64, 77, 25);rgb(25, 77, 38);rgb(25, 64, 77);rgb(38, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(32, 21, 64);rgb(64, 21, 53);rgb(64, 32, 21);rgb(53, 64, 21);rgb(21, 64, 32);rgb(21, 53, 64);rgb(32, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(27, 18, 54);rgb(54, 18, 45);rgb(54, 27, 18);rgb(45, 54, 18);rgb(18, 54, 27);rgb(18, 45, 54);rgb(27, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(360 373.57)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(38, 25, 77);rgb(77, 25, 64);rgb(77, 38, 25);rgb(64, 77, 25);rgb(25, 77, 38);rgb(25, 64, 77);rgb(38, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(32, 21, 64);rgb(64, 21, 53);rgb(64, 32, 21);rgb(53, 64, 21);rgb(21, 64, 32);rgb(21, 53, 64);rgb(32, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(27, 18, 54);rgb(54, 18, 45);rgb(54, 27, 18);rgb(45, 54, 18);rgb(18, 54, 27);rgb(18, 45, 54);rgb(27, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(340 361.88)"><animateTransform attributeName="transform" type="translate" values="340 385.12;340 361.88" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(67, 45, 134);rgb(134, 45, 112);rgb(134, 67, 45);rgb(112, 134, 45);rgb(45, 134, 67);rgb(45, 112, 134);rgb(67, 45, 134)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="22.72" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(56, 37, 112);rgb(112, 37, 93);rgb(112, 56, 37);rgb(93, 112, 37);rgb(37, 112, 56);rgb(37, 93, 112);rgb(56, 37, 112)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;22.72" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="22.72" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(47, 31, 94);rgb(94, 31, 78);rgb(94, 47, 31);rgb(78, 94, 31);rgb(31, 94, 47);rgb(31, 78, 94);rgb(47, 31, 94)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;22.72" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(320 396.67)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(38, 25, 77);rgb(77, 25, 64);rgb(77, 38, 25);rgb(64, 77, 25);rgb(25, 77, 38);rgb(25, 64, 77);rgb(38, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(32, 21, 64);rgb(64, 21, 53);rgb(64, 32, 21);rgb(53, 64, 21);rgb(21, 64, 32);rgb(21, 53, 64);rgb(32, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(27, 18, 54);rgb(54, 18, 45);rgb(54, 27, 18);rgb(45, 54, 18);rgb(18, 54, 27);rgb(18, 45, 54);rgb(27, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(460 324.98)"><animateTransform attributeName="transform" type="translate" values="460 338.93;460 324.98" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(48, 38, 115);rgb(115, 38, 105);rgb(115, 48, 38);rgb(105, 115, 38);rgb(38, 115, 48);rgb(38, 105, 115);rgb(48, 38, 115)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="14.68" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(41, 32, 96);rgb(96, 32, 87);rgb(96, 41, 32);rgb(87, 96, 32);rgb(32, 96, 41);rgb(32, 87, 96);rgb(41, 32, 96)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;14.68" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="14.68" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(34, 27, 80);rgb(80, 27, 73);rgb(80, 34, 27);rgb(73, 80, 27);rgb(27, 80, 34);rgb(27, 73, 80);rgb(34, 27, 80)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;14.68" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(440 347.43)"><animateTransform attributeName="transform" type="translate" values="440 350.48;440 347.43" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(48, 38, 115);rgb(115, 38, 105);rgb(115, 48, 38);rgb(105, 115, 38);rgb(38, 115, 48);rgb(38, 105, 115);rgb(48, 38, 115)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="5.24" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(41, 32, 96);rgb(96, 32, 87);rgb(96, 41, 32);rgb(87, 96, 32);rgb(32, 96, 41);rgb(32, 87, 96);rgb(41, 32, 96)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;5.24" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="5.24" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(34, 27, 80);rgb(80, 27, 73);rgb(80, 34, 27);rgb(73, 80, 27);rgb(27, 80, 34);rgb(27, 73, 80);rgb(34, 27, 80)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;5.24" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(420 353.29)"><animateTransform attributeName="transform" type="translate" values="420 362.03;420 353.29" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(48, 38, 115);rgb(115, 38, 105);rgb(115, 48, 38);rgb(105, 115, 38);rgb(38, 115, 48);rgb(38, 105, 115);rgb(48, 38, 115)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="10.17" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(41, 32, 96);rgb(96, 32, 87);rgb(96, 41, 32);rgb(87, 96, 32);rgb(32, 96, 41);rgb(32, 87, 96);rgb(41, 32, 96)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;10.17" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="10.17" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(34, 27, 80);rgb(80, 27, 73);rgb(80, 34, 27);rgb(73, 80, 27);rgb(27, 80, 34);rgb(27, 73, 80);rgb(34, 27, 80)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;10.17" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(400 348.22)"><animateTransform attributeName="transform" type="translate" values="400 373.57;400 348.22" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(57, 45, 134);rgb(134, 45, 122);rgb(134, 57, 45);rgb(122, 134, 45);rgb(45, 134, 57);rgb(45, 122, 134);rgb(57, 45, 134)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="24.56" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(47, 37, 112);rgb(112, 37, 102);rgb(112, 47, 37);rgb(102, 112, 37);rgb(37, 112, 47);rgb(37, 102, 112);rgb(47, 37, 112)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;24.56" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="24.56" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(40, 31, 94);rgb(94, 31, 85);rgb(94, 40, 31);rgb(85, 94, 31);rgb(31, 94, 40);rgb(31, 85, 94);rgb(40, 31, 94)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;24.56" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(380 357.71)"><animateTransform attributeName="transform" type="translate" values="380 385.12;380 357.71" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(57, 45, 134);rgb(134, 45, 122);rgb(134, 57, 45);rgb(122, 134, 45);rgb(45, 134, 57);rgb(45, 122, 134);rgb(57, 45, 134)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="26.33" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(47, 37, 112);rgb(112, 37, 102);rgb(112, 47, 37);rgb(102, 112, 37);rgb(37, 112, 47);rgb(37, 102, 112);rgb(47, 37, 112)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;26.33" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="26.33" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(40, 31, 94);rgb(94, 31, 85);rgb(94, 40, 31);rgb(85, 94, 31);rgb(31, 94, 40);rgb(31, 85, 94);rgb(40, 31, 94)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;26.33" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(360 375.62)"><animateTransform attributeName="transform" type="translate" values="360 396.67;360 375.62" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(57, 45, 134);rgb(134, 45, 122);rgb(134, 57, 45);rgb(122, 134, 45);rgb(45, 134, 57);rgb(45, 122, 134);rgb(57, 45, 134)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="20.82" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(47, 37, 112);rgb(112, 37, 102);rgb(112, 47, 37);rgb(102, 112, 37);rgb(37, 112, 47);rgb(37, 102, 112);rgb(47, 37, 112)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;20.82" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="20.82" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(40, 31, 94);rgb(94, 31, 85);rgb(94, 40, 31);rgb(85, 94, 31);rgb(31, 94, 40);rgb(31, 85, 94);rgb(40, 31, 94)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;20.82" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(340 382.86)"><animateTransform attributeName="transform" type="translate" values="340 408.21;340 382.86" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(57, 45, 134);rgb(134, 45, 122);rgb(134, 57, 45);rgb(122, 134, 45);rgb(45, 134, 57);rgb(45, 122, 134);rgb(57, 45, 134)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="24.56" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(47, 37, 112);rgb(112, 37, 102);rgb(112, 47, 37);rgb(102, 112, 37);rgb(37, 112, 47);rgb(37, 102, 112);rgb(47, 37, 112)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;24.56" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="24.56" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(40, 31, 94);rgb(94, 31, 85);rgb(94, 40, 31);rgb(85, 94, 31);rgb(31, 94, 40);rgb(31, 85, 94);rgb(40, 31, 94)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;24.56" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(480 334.07)"><animateTransform attributeName="transform" type="translate" values="480 350.48;480 334.07" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(40, 38, 115);rgb(115, 38, 113);rgb(115, 40, 38);rgb(113, 115, 38);rgb(38, 115, 40);rgb(38, 113, 115);rgb(40, 38, 115)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="16.81" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(33, 32, 96);rgb(96, 32, 95);rgb(96, 33, 32);rgb(95, 96, 32);rgb(32, 96, 33);rgb(32, 95, 96);rgb(33, 32, 96)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;16.81" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="16.81" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(28, 27, 80);rgb(80, 27, 79);rgb(80, 28, 27);rgb(79, 80, 27);rgb(27, 80, 28);rgb(27, 79, 80);rgb(28, 27, 80)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;16.81" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(460 362.03)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(26, 25, 77);rgb(77, 25, 76);rgb(77, 26, 25);rgb(76, 77, 25);rgb(25, 77, 26);rgb(25, 76, 77);rgb(26, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(22, 21, 64);rgb(64, 21, 63);rgb(64, 22, 21);rgb(63, 64, 21);rgb(21, 64, 22);rgb(21, 63, 64);rgb(22, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 18, 54);rgb(54, 18, 53);rgb(54, 18, 18);rgb(53, 54, 18);rgb(18, 54, 18);rgb(18, 53, 54);rgb(18, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(440 373.57)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(26, 25, 77);rgb(77, 25, 76);rgb(77, 26, 25);rgb(76, 77, 25);rgb(25, 77, 26);rgb(25, 76, 77);rgb(26, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(22, 21, 64);rgb(64, 21, 63);rgb(64, 22, 21);rgb(63, 64, 21);rgb(21, 64, 22);rgb(21, 63, 64);rgb(22, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 18, 54);rgb(54, 18, 53);rgb(54, 18, 18);rgb(53, 54, 18);rgb(18, 54, 18);rgb(18, 53, 54);rgb(18, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(420 385.12)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(26, 25, 77);rgb(77, 25, 76);rgb(77, 26, 25);rgb(76, 77, 25);rgb(25, 77, 26);rgb(25, 76, 77);rgb(26, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(22, 21, 64);rgb(64, 21, 63);rgb(64, 22, 21);rgb(63, 64, 21);rgb(21, 64, 22);rgb(21, 63, 64);rgb(22, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 18, 54);rgb(54, 18, 53);rgb(54, 18, 18);rgb(53, 54, 18);rgb(18, 54, 18);rgb(18, 53, 54);rgb(18, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(400 375.62)"><animateTransform attributeName="transform" type="translate" values="400 396.67;400 375.62" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(46, 45, 134);rgb(134, 45, 132);rgb(134, 46, 45);rgb(132, 134, 45);rgb(45, 134, 46);rgb(45, 132, 134);rgb(46, 45, 134)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="20.82" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(39, 37, 112);rgb(112, 37, 111);rgb(112, 39, 37);rgb(111, 112, 37);rgb(37, 112, 39);rgb(37, 111, 112);rgb(39, 37, 112)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;20.82" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="20.82" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(32, 31, 94);rgb(94, 31, 93);rgb(94, 32, 31);rgb(93, 94, 31);rgb(31, 94, 32);rgb(31, 93, 94);rgb(32, 31, 94)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;20.82" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(380 391.81)"><animateTransform attributeName="transform" type="translate" values="380 408.21;380 391.81" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(40, 38, 115);rgb(115, 38, 113);rgb(115, 40, 38);rgb(113, 115, 38);rgb(38, 115, 40);rgb(38, 113, 115);rgb(40, 38, 115)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="16.81" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(33, 32, 96);rgb(96, 32, 95);rgb(96, 33, 32);rgb(95, 96, 32);rgb(32, 96, 33);rgb(32, 95, 96);rgb(33, 32, 96)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;16.81" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="16.81" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(28, 27, 80);rgb(80, 27, 79);rgb(80, 28, 27);rgb(79, 80, 27);rgb(27, 80, 28);rgb(27, 79, 80);rgb(28, 27, 80)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;16.81" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(360 419.76)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(26, 25, 77);rgb(77, 25, 76);rgb(77, 26, 25);rgb(76, 77, 25);rgb(25, 77, 26);rgb(25, 76, 77);rgb(26, 25, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(22, 21, 64);rgb(64, 21, 63);rgb(64, 22, 21);rgb(63, 64, 21);rgb(21, 64, 22);rgb(21, 63, 64);rgb(22, 21, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 18, 54);rgb(54, 18, 53);rgb(54, 18, 18);rgb(53, 54, 18);rgb(18, 54, 18);rgb(18, 53, 54);rgb(18, 18, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(500 362.03)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 31, 77);rgb(71, 25, 77);rgb(77, 25, 31);rgb(77, 71, 25);rgb(31, 77, 25);rgb(25, 77, 71);rgb(25, 31, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 26, 64);rgb(60, 21, 64);rgb(64, 21, 26);rgb(64, 60, 21);rgb(26, 64, 21);rgb(21, 64, 60);rgb(21, 26, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 21, 54);rgb(50, 18, 54);rgb(54, 18, 21);rgb(54, 50, 18);rgb(21, 54, 18);rgb(18, 54, 50);rgb(18, 21, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(480 373.57)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 31, 77);rgb(71, 25, 77);rgb(77, 25, 31);rgb(77, 71, 25);rgb(31, 77, 25);rgb(25, 77, 71);rgb(25, 31, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 26, 64);rgb(60, 21, 64);rgb(64, 21, 26);rgb(64, 60, 21);rgb(26, 64, 21);rgb(21, 64, 60);rgb(21, 26, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 21, 54);rgb(50, 18, 54);rgb(54, 18, 21);rgb(54, 50, 18);rgb(21, 54, 18);rgb(18, 54, 50);rgb(18, 21, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(460 385.12)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 31, 77);rgb(71, 25, 77);rgb(77, 25, 31);rgb(77, 71, 25);rgb(31, 77, 25);rgb(25, 77, 71);rgb(25, 31, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 26, 64);rgb(60, 21, 64);rgb(64, 21, 26);rgb(64, 60, 21);rgb(26, 64, 21);rgb(21, 64, 60);rgb(21, 26, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 21, 54);rgb(50, 18, 54);rgb(54, 18, 21);rgb(54, 50, 18);rgb(21, 54, 18);rgb(18, 54, 50);rgb(18, 21, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(440 396.67)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 31, 77);rgb(71, 25, 77);rgb(77, 25, 31);rgb(77, 71, 25);rgb(31, 77, 25);rgb(25, 77, 71);rgb(25, 31, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 26, 64);rgb(60, 21, 64);rgb(64, 21, 26);rgb(64, 60, 21);rgb(26, 64, 21);rgb(21, 64, 60);rgb(21, 26, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 21, 54);rgb(50, 18, 54);rgb(54, 18, 21);rgb(54, 50, 18);rgb(21, 54, 18);rgb(18, 54, 50);rgb(18, 21, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(420 408.21)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 31, 77);rgb(71, 25, 77);rgb(77, 25, 31);rgb(77, 71, 25);rgb(31, 77, 25);rgb(25, 77, 71);rgb(25, 31, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 26, 64);rgb(60, 21, 64);rgb(64, 21, 26);rgb(64, 60, 21);rgb(26, 64, 21);rgb(21, 64, 60);rgb(21, 26, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 21, 54);rgb(50, 18, 54);rgb(54, 18, 21);rgb(54, 50, 18);rgb(21, 54, 18);rgb(18, 54, 50);rgb(18, 21, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(400 419.76)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 31, 77);rgb(71, 25, 77);rgb(77, 25, 31);rgb(77, 71, 25);rgb(31, 77, 25);rgb(25, 77, 71);rgb(25, 31, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 26, 64);rgb(60, 21, 64);rgb(64, 21, 26);rgb(64, 60, 21);rgb(26, 64, 21);rgb(21, 64, 60);rgb(21, 26, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 21, 54);rgb(50, 18, 54);rgb(54, 18, 21);rgb(54, 50, 18);rgb(21, 54, 18);rgb(18, 54, 50);rgb(18, 21, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(380 431.31)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 31, 77);rgb(71, 25, 77);rgb(77, 25, 31);rgb(77, 71, 25);rgb(31, 77, 25);rgb(25, 77, 71);rgb(25, 31, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 26, 64);rgb(60, 21, 64);rgb(64, 21, 26);rgb(64, 60, 21);rgb(26, 64, 21);rgb(21, 64, 60);rgb(21, 26, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 21, 54);rgb(50, 18, 54);rgb(54, 18, 21);rgb(54, 50, 18);rgb(21, 54, 18);rgb(18, 54, 50);rgb(18, 21, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(520 373.57)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 37, 77);rgb(65, 25, 77);rgb(77, 25, 37);rgb(77, 65, 25);rgb(37, 77, 25);rgb(25, 77, 65);rgb(25, 37, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 31, 64);rgb(55, 21, 64);rgb(64, 21, 31);rgb(64, 55, 21);rgb(31, 64, 21);rgb(21, 64, 55);rgb(21, 31, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 26, 54);rgb(46, 18, 54);rgb(54, 18, 26);rgb(54, 46, 18);rgb(26, 54, 18);rgb(18, 54, 46);rgb(18, 26, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(500 385.12)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 37, 77);rgb(65, 25, 77);rgb(77, 25, 37);rgb(77, 65, 25);rgb(37, 77, 25);rgb(25, 77, 65);rgb(25, 37, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 31, 64);rgb(55, 21, 64);rgb(64, 21, 31);rgb(64, 55, 21);rgb(31, 64, 21);rgb(21, 64, 55);rgb(21, 31, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 26, 54);rgb(46, 18, 54);rgb(54, 18, 26);rgb(54, 46, 18);rgb(26, 54, 18);rgb(18, 54, 46);rgb(18, 26, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(480 396.67)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 37, 77);rgb(65, 25, 77);rgb(77, 25, 37);rgb(77, 65, 25);rgb(37, 77, 25);rgb(25, 77, 65);rgb(25, 37, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 31, 64);rgb(55, 21, 64);rgb(64, 21, 31);rgb(64, 55, 21);rgb(31, 64, 21);rgb(21, 64, 55);rgb(21, 31, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 26, 54);rgb(46, 18, 54);rgb(54, 18, 26);rgb(54, 46, 18);rgb(26, 54, 18);rgb(18, 54, 46);rgb(18, 26, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(460 408.21)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 37, 77);rgb(65, 25, 77);rgb(77, 25, 37);rgb(77, 65, 25);rgb(37, 77, 25);rgb(25, 77, 65);rgb(25, 37, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 31, 64);rgb(55, 21, 64);rgb(64, 21, 31);rgb(64, 55, 21);rgb(31, 64, 21);rgb(21, 64, 55);rgb(21, 31, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 26, 54);rgb(46, 18, 54);rgb(54, 18, 26);rgb(54, 46, 18);rgb(26, 54, 18);rgb(18, 54, 46);rgb(18, 26, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(440 419.76)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 37, 77);rgb(65, 25, 77);rgb(77, 25, 37);rgb(77, 65, 25);rgb(37, 77, 25);rgb(25, 77, 65);rgb(25, 37, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 31, 64);rgb(55, 21, 64);rgb(64, 21, 31);rgb(64, 55, 21);rgb(31, 64, 21);rgb(21, 64, 55);rgb(21, 31, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 26, 54);rgb(46, 18, 54);rgb(54, 18, 26);rgb(54, 46, 18);rgb(26, 54, 18);rgb(18, 54, 46);rgb(18, 26, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(420 431.31)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 37, 77);rgb(65, 25, 77);rgb(77, 25, 37);rgb(77, 65, 25);rgb(37, 77, 25);rgb(25, 77, 65);rgb(25, 37, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 31, 64);rgb(55, 21, 64);rgb(64, 21, 31);rgb(64, 55, 21);rgb(31, 64, 21);rgb(21, 64, 55);rgb(21, 31, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 26, 54);rgb(46, 18, 54);rgb(54, 18, 26);rgb(54, 46, 18);rgb(26, 54, 18);rgb(18, 54, 46);rgb(18, 26, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(400 442.85)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 37, 77);rgb(65, 25, 77);rgb(77, 25, 37);rgb(77, 65, 25);rgb(37, 77, 25);rgb(25, 77, 65);rgb(25, 37, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 31, 64);rgb(55, 21, 64);rgb(64, 21, 31);rgb(64, 55, 21);rgb(31, 64, 21);rgb(21, 64, 55);rgb(21, 31, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 26, 54);rgb(46, 18, 54);rgb(54, 18, 26);rgb(54, 46, 18);rgb(26, 54, 18);rgb(18, 54, 46);rgb(18, 26, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(540 385.12)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 43, 77);rgb(60, 25, 77);rgb(77, 25, 43);rgb(77, 60, 25);rgb(43, 77, 25);rgb(25, 77, 60);rgb(25, 43, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 36, 64);rgb(50, 21, 64);rgb(64, 21, 36);rgb(64, 50, 21);rgb(36, 64, 21);rgb(21, 64, 50);rgb(21, 36, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 30, 54);rgb(42, 18, 54);rgb(54, 18, 30);rgb(54, 42, 18);rgb(30, 54, 18);rgb(18, 54, 42);rgb(18, 30, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(520 396.67)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 43, 77);rgb(60, 25, 77);rgb(77, 25, 43);rgb(77, 60, 25);rgb(43, 77, 25);rgb(25, 77, 60);rgb(25, 43, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 36, 64);rgb(50, 21, 64);rgb(64, 21, 36);rgb(64, 50, 21);rgb(36, 64, 21);rgb(21, 64, 50);rgb(21, 36, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 30, 54);rgb(42, 18, 54);rgb(54, 18, 30);rgb(54, 42, 18);rgb(30, 54, 18);rgb(18, 54, 42);rgb(18, 30, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(500 408.21)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 43, 77);rgb(60, 25, 77);rgb(77, 25, 43);rgb(77, 60, 25);rgb(43, 77, 25);rgb(25, 77, 60);rgb(25, 43, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 36, 64);rgb(50, 21, 64);rgb(64, 21, 36);rgb(64, 50, 21);rgb(36, 64, 21);rgb(21, 64, 50);rgb(21, 36, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 30, 54);rgb(42, 18, 54);rgb(54, 18, 30);rgb(54, 42, 18);rgb(30, 54, 18);rgb(18, 54, 42);rgb(18, 30, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(480 419.76)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 43, 77);rgb(60, 25, 77);rgb(77, 25, 43);rgb(77, 60, 25);rgb(43, 77, 25);rgb(25, 77, 60);rgb(25, 43, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 36, 64);rgb(50, 21, 64);rgb(64, 21, 36);rgb(64, 50, 21);rgb(36, 64, 21);rgb(21, 64, 50);rgb(21, 36, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 30, 54);rgb(42, 18, 54);rgb(54, 18, 30);rgb(54, 42, 18);rgb(30, 54, 18);rgb(18, 54, 42);rgb(18, 30, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(460 431.31)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 43, 77);rgb(60, 25, 77);rgb(77, 25, 43);rgb(77, 60, 25);rgb(43, 77, 25);rgb(25, 77, 60);rgb(25, 43, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 36, 64);rgb(50, 21, 64);rgb(64, 21, 36);rgb(64, 50, 21);rgb(36, 64, 21);rgb(21, 64, 50);rgb(21, 36, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 30, 54);rgb(42, 18, 54);rgb(54, 18, 30);rgb(54, 42, 18);rgb(30, 54, 18);rgb(18, 54, 42);rgb(18, 30, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(440 442.85)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 43, 77);rgb(60, 25, 77);rgb(77, 25, 43);rgb(77, 60, 25);rgb(43, 77, 25);rgb(25, 77, 60);rgb(25, 43, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 36, 64);rgb(50, 21, 64);rgb(64, 21, 36);rgb(64, 50, 21);rgb(36, 64, 21);rgb(21, 64, 50);rgb(21, 36, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 30, 54);rgb(42, 18, 54);rgb(54, 18, 30);rgb(54, 42, 18);rgb(30, 54, 18);rgb(18, 54, 42);rgb(18, 30, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(420 454.4)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 43, 77);rgb(60, 25, 77);rgb(77, 25, 43);rgb(77, 60, 25);rgb(43, 77, 25);rgb(25, 77, 60);rgb(25, 43, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 36, 64);rgb(50, 21, 64);rgb(64, 21, 36);rgb(64, 50, 21);rgb(36, 64, 21);rgb(21, 64, 50);rgb(21, 36, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 30, 54);rgb(42, 18, 54);rgb(54, 18, 30);rgb(54, 42, 18);rgb(30, 54, 18);rgb(18, 54, 42);rgb(18, 30, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(560 396.67)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 48, 77);rgb(54, 25, 77);rgb(77, 25, 48);rgb(77, 54, 25);rgb(48, 77, 25);rgb(25, 77, 54);rgb(25, 48, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 41, 64);rgb(45, 21, 64);rgb(64, 21, 41);rgb(64, 45, 21);rgb(41, 64, 21);rgb(21, 64, 45);rgb(21, 41, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 34, 54);rgb(37, 18, 54);rgb(54, 18, 34);rgb(54, 37, 18);rgb(34, 54, 18);rgb(18, 54, 37);rgb(18, 34, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(540 408.21)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 48, 77);rgb(54, 25, 77);rgb(77, 25, 48);rgb(77, 54, 25);rgb(48, 77, 25);rgb(25, 77, 54);rgb(25, 48, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 41, 64);rgb(45, 21, 64);rgb(64, 21, 41);rgb(64, 45, 21);rgb(41, 64, 21);rgb(21, 64, 45);rgb(21, 41, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 34, 54);rgb(37, 18, 54);rgb(54, 18, 34);rgb(54, 37, 18);rgb(34, 54, 18);rgb(18, 54, 37);rgb(18, 34, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(520 419.76)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 48, 77);rgb(54, 25, 77);rgb(77, 25, 48);rgb(77, 54, 25);rgb(48, 77, 25);rgb(25, 77, 54);rgb(25, 48, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 41, 64);rgb(45, 21, 64);rgb(64, 21, 41);rgb(64, 45, 21);rgb(41, 64, 21);rgb(21, 64, 45);rgb(21, 41, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 34, 54);rgb(37, 18, 54);rgb(54, 18, 34);rgb(54, 37, 18);rgb(34, 54, 18);rgb(18, 54, 37);rgb(18, 34, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(500 431.31)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 48, 77);rgb(54, 25, 77);rgb(77, 25, 48);rgb(77, 54, 25);rgb(48, 77, 25);rgb(25, 77, 54);rgb(25, 48, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 41, 64);rgb(45, 21, 64);rgb(64, 21, 41);rgb(64, 45, 21);rgb(41, 64, 21);rgb(21, 64, 45);rgb(21, 41, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 34, 54);rgb(37, 18, 54);rgb(54, 18, 34);rgb(54, 37, 18);rgb(34, 54, 18);rgb(18, 54, 37);rgb(18, 34, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(480 442.85)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 48, 77);rgb(54, 25, 77);rgb(77, 25, 48);rgb(77, 54, 25);rgb(48, 77, 25);rgb(25, 77, 54);rgb(25, 48, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 41, 64);rgb(45, 21, 64);rgb(64, 21, 41);rgb(64, 45, 21);rgb(41, 64, 21);rgb(21, 64, 45);rgb(21, 41, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 34, 54);rgb(37, 18, 54);rgb(54, 18, 34);rgb(54, 37, 18);rgb(34, 54, 18);rgb(18, 54, 37);rgb(18, 34, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(460 454.4)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 48, 77);rgb(54, 25, 77);rgb(77, 25, 48);rgb(77, 54, 25);rgb(48, 77, 25);rgb(25, 77, 54);rgb(25, 48, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 41, 64);rgb(45, 21, 64);rgb(64, 21, 41);rgb(64, 45, 21);rgb(41, 64, 21);rgb(21, 64, 45);rgb(21, 41, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 34, 54);rgb(37, 18, 54);rgb(54, 18, 34);rgb(54, 37, 18);rgb(34, 54, 18);rgb(18, 54, 37);rgb(18, 34, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(440 465.95)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 48, 77);rgb(54, 25, 77);rgb(77, 25, 48);rgb(77, 54, 25);rgb(48, 77, 25);rgb(25, 77, 54);rgb(25, 48, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 41, 64);rgb(45, 21, 64);rgb(64, 21, 41);rgb(64, 45, 21);rgb(41, 64, 21);rgb(21, 64, 45);rgb(21, 41, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 34, 54);rgb(37, 18, 54);rgb(54, 18, 34);rgb(54, 37, 18);rgb(34, 54, 18);rgb(18, 54, 37);rgb(18, 34, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(580 408.21)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 54, 77);rgb(48, 25, 77);rgb(77, 25, 54);rgb(77, 48, 25);rgb(54, 77, 25);rgb(25, 77, 48);rgb(25, 54, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 46, 64);rgb(40, 21, 64);rgb(64, 21, 46);rgb(64, 40, 21);rgb(46, 64, 21);rgb(21, 64, 40);rgb(21, 46, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 38, 54);rgb(33, 18, 54);rgb(54, 18, 38);rgb(54, 33, 18);rgb(38, 54, 18);rgb(18, 54, 33);rgb(18, 38, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(560 413.8)"><animateTransform attributeName="transform" type="translate" values="560 419.76;560 413.8" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(38, 82, 115);rgb(71, 38, 115);rgb(115, 38, 82);rgb(115, 71, 38);rgb(82, 115, 38);rgb(38, 115, 71);rgb(38, 82, 115)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="7.76" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(32, 68, 96);rgb(60, 32, 96);rgb(96, 32, 68);rgb(96, 60, 32);rgb(68, 96, 32);rgb(32, 96, 60);rgb(32, 68, 96)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;7.76" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="7.76" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(27, 57, 80);rgb(50, 27, 80);rgb(80, 27, 57);rgb(80, 50, 27);rgb(57, 80, 27);rgb(27, 80, 50);rgb(27, 57, 80)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;7.76" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(540 431.31)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 54, 77);rgb(48, 25, 77);rgb(77, 25, 54);rgb(77, 48, 25);rgb(54, 77, 25);rgb(25, 77, 48);rgb(25, 54, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 46, 64);rgb(40, 21, 64);rgb(64, 21, 46);rgb(64, 40, 21);rgb(46, 64, 21);rgb(21, 64, 40);rgb(21, 46, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 38, 54);rgb(33, 18, 54);rgb(54, 18, 38);rgb(54, 33, 18);rgb(38, 54, 18);rgb(18, 54, 33);rgb(18, 38, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(520 442.85)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 54, 77);rgb(48, 25, 77);rgb(77, 25, 54);rgb(77, 48, 25);rgb(54, 77, 25);rgb(25, 77, 48);rgb(25, 54, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 46, 64);rgb(40, 21, 64);rgb(64, 21, 46);rgb(64, 40, 21);rgb(46, 64, 21);rgb(21, 64, 40);rgb(21, 46, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 38, 54);rgb(33, 18, 54);rgb(54, 18, 38);rgb(54, 33, 18);rgb(38, 54, 18);rgb(18, 54, 33);rgb(18, 38, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(500 454.4)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 54, 77);rgb(48, 25, 77);rgb(77, 25, 54);rgb(77, 48, 25);rgb(54, 77, 25);rgb(25, 77, 48);rgb(25, 54, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 46, 64);rgb(40, 21, 64);rgb(64, 21, 46);rgb(64, 40, 21);rgb(46, 64, 21);rgb(21, 64, 40);rgb(21, 46, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 38, 54);rgb(33, 18, 54);rgb(54, 18, 38);rgb(54, 33, 18);rgb(38, 54, 18);rgb(18, 54, 33);rgb(18, 38, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(480 462.9)"><animateTransform attributeName="transform" type="translate" values="480 465.95;480 462.9" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(38, 82, 115);rgb(71, 38, 115);rgb(115, 38, 82);rgb(115, 71, 38);rgb(82, 115, 38);rgb(38, 115, 71);rgb(38, 82, 115)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="5.24" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(32, 68, 96);rgb(60, 32, 96);rgb(96, 32, 68);rgb(96, 60, 32);rgb(68, 96, 32);rgb(32, 96, 60);rgb(32, 68, 96)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;5.24" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="5.24" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(27, 57, 80);rgb(50, 27, 80);rgb(80, 27, 57);rgb(80, 50, 27);rgb(57, 80, 27);rgb(27, 80, 50);rgb(27, 57, 80)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;5.24" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(460 463.54)"><animateTransform attributeName="transform" type="translate" values="460 477.5;460 463.54" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(38, 82, 115);rgb(71, 38, 115);rgb(115, 38, 82);rgb(115, 71, 38);rgb(82, 115, 38);rgb(38, 115, 71);rgb(38, 82, 115)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="14.68" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(32, 68, 96);rgb(60, 32, 96);rgb(96, 32, 68);rgb(96, 60, 32);rgb(68, 96, 32);rgb(32, 96, 60);rgb(32, 68, 96)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;14.68" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="14.68" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(27, 57, 80);rgb(50, 27, 80);rgb(80, 27, 57);rgb(80, 50, 27);rgb(57, 80, 27);rgb(27, 80, 50);rgb(27, 57, 80)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;14.68" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(600 419.76)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 60, 77);rgb(42, 25, 77);rgb(77, 25, 60);rgb(77, 42, 25);rgb(60, 77, 25);rgb(25, 77, 42);rgb(25, 60, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 50, 64);rgb(35, 21, 64);rgb(64, 21, 50);rgb(64, 35, 21);rgb(50, 64, 21);rgb(21, 64, 35);rgb(21, 50, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 42, 54);rgb(29, 18, 54);rgb(54, 18, 42);rgb(54, 29, 18);rgb(42, 54, 18);rgb(18, 54, 29);rgb(18, 42, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(580 431.31)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 60, 77);rgb(42, 25, 77);rgb(77, 25, 60);rgb(77, 42, 25);rgb(60, 77, 25);rgb(25, 77, 42);rgb(25, 60, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 50, 64);rgb(35, 21, 64);rgb(64, 21, 50);rgb(64, 35, 21);rgb(50, 64, 21);rgb(21, 64, 35);rgb(21, 50, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 42, 54);rgb(29, 18, 54);rgb(54, 18, 42);rgb(54, 29, 18);rgb(42, 54, 18);rgb(18, 54, 29);rgb(18, 42, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(560 442.85)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 60, 77);rgb(42, 25, 77);rgb(77, 25, 60);rgb(77, 42, 25);rgb(60, 77, 25);rgb(25, 77, 42);rgb(25, 60, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 50, 64);rgb(35, 21, 64);rgb(64, 21, 50);rgb(64, 35, 21);rgb(50, 64, 21);rgb(21, 64, 35);rgb(21, 50, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 42, 54);rgb(29, 18, 54);rgb(54, 18, 42);rgb(54, 29, 18);rgb(42, 54, 18);rgb(18, 54, 29);rgb(18, 42, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(540 454.4)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 60, 77);rgb(42, 25, 77);rgb(77, 25, 60);rgb(77, 42, 25);rgb(60, 77, 25);rgb(25, 77, 42);rgb(25, 60, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 50, 64);rgb(35, 21, 64);rgb(64, 21, 50);rgb(64, 35, 21);rgb(50, 64, 21);rgb(21, 64, 35);rgb(21, 50, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 42, 54);rgb(29, 18, 54);rgb(54, 18, 42);rgb(54, 29, 18);rgb(42, 54, 18);rgb(18, 54, 29);rgb(18, 42, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(520 465.95)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 60, 77);rgb(42, 25, 77);rgb(77, 25, 60);rgb(77, 42, 25);rgb(60, 77, 25);rgb(25, 77, 42);rgb(25, 60, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 50, 64);rgb(35, 21, 64);rgb(64, 21, 50);rgb(64, 35, 21);rgb(50, 64, 21);rgb(21, 64, 35);rgb(21, 50, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 42, 54);rgb(29, 18, 54);rgb(54, 18, 42);rgb(54, 29, 18);rgb(42, 54, 18);rgb(18, 54, 29);rgb(18, 42, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(500 477.5)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 60, 77);rgb(42, 25, 77);rgb(77, 25, 60);rgb(77, 42, 25);rgb(60, 77, 25);rgb(25, 77, 42);rgb(25, 60, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 50, 64);rgb(35, 21, 64);rgb(64, 21, 50);rgb(64, 35, 21);rgb(50, 64, 21);rgb(21, 64, 35);rgb(21, 50, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 42, 54);rgb(29, 18, 54);rgb(54, 18, 42);rgb(54, 29, 18);rgb(42, 54, 18);rgb(18, 54, 29);rgb(18, 42, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(480 489.04)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 60, 77);rgb(42, 25, 77);rgb(77, 25, 60);rgb(77, 42, 25);rgb(60, 77, 25);rgb(25, 77, 42);rgb(25, 60, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 50, 64);rgb(35, 21, 64);rgb(64, 21, 50);rgb(64, 35, 21);rgb(50, 64, 21);rgb(21, 64, 35);rgb(21, 50, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 42, 54);rgb(29, 18, 54);rgb(54, 18, 42);rgb(54, 29, 18);rgb(42, 54, 18);rgb(18, 54, 29);rgb(18, 42, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(620 431.31)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 66, 77);rgb(36, 25, 77);rgb(77, 25, 66);rgb(77, 36, 25);rgb(66, 77, 25);rgb(25, 77, 36);rgb(25, 66, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 55, 64);rgb(30, 21, 64);rgb(64, 21, 55);rgb(64, 30, 21);rgb(55, 64, 21);rgb(21, 64, 30);rgb(21, 55, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 46, 54);rgb(25, 18, 54);rgb(54, 18, 46);rgb(54, 25, 18);rgb(46, 54, 18);rgb(18, 54, 25);rgb(18, 46, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(600 442.85)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 66, 77);rgb(36, 25, 77);rgb(77, 25, 66);rgb(77, 36, 25);rgb(66, 77, 25);rgb(25, 77, 36);rgb(25, 66, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 55, 64);rgb(30, 21, 64);rgb(64, 21, 55);rgb(64, 30, 21);rgb(55, 64, 21);rgb(21, 64, 30);rgb(21, 55, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 46, 54);rgb(25, 18, 54);rgb(54, 18, 46);rgb(54, 25, 18);rgb(46, 54, 18);rgb(18, 54, 25);rgb(18, 46, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(580 454.4)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 66, 77);rgb(36, 25, 77);rgb(77, 25, 66);rgb(77, 36, 25);rgb(66, 77, 25);rgb(25, 77, 36);rgb(25, 66, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 55, 64);rgb(30, 21, 64);rgb(64, 21, 55);rgb(64, 30, 21);rgb(55, 64, 21);rgb(21, 64, 30);rgb(21, 55, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 46, 54);rgb(25, 18, 54);rgb(54, 18, 46);rgb(54, 25, 18);rgb(46, 54, 18);rgb(18, 54, 25);rgb(18, 46, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(560 459.99)"><animateTransform attributeName="transform" type="translate" values="560 465.95;560 459.99" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(38, 99, 115);rgb(54, 38, 115);rgb(115, 38, 99);rgb(115, 54, 38);rgb(99, 115, 38);rgb(38, 115, 54);rgb(38, 99, 115)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="7.76" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(32, 83, 96);rgb(45, 32, 96);rgb(96, 32, 83);rgb(96, 45, 32);rgb(83, 96, 32);rgb(32, 96, 45);rgb(32, 83, 96)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;7.76" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="7.76" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(27, 70, 80);rgb(37, 27, 80);rgb(80, 27, 70);rgb(80, 37, 27);rgb(70, 80, 27);rgb(27, 80, 37);rgb(27, 70, 80)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;7.76" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(540 477.5)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 66, 77);rgb(36, 25, 77);rgb(77, 25, 66);rgb(77, 36, 25);rgb(66, 77, 25);rgb(25, 77, 36);rgb(25, 66, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 55, 64);rgb(30, 21, 64);rgb(64, 21, 55);rgb(64, 30, 21);rgb(55, 64, 21);rgb(21, 64, 30);rgb(21, 55, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 46, 54);rgb(25, 18, 54);rgb(54, 18, 46);rgb(54, 25, 18);rgb(46, 54, 18);rgb(18, 54, 25);rgb(18, 46, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(520 489.04)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 66, 77);rgb(36, 25, 77);rgb(77, 25, 66);rgb(77, 36, 25);rgb(66, 77, 25);rgb(25, 77, 36);rgb(25, 66, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 55, 64);rgb(30, 21, 64);rgb(64, 21, 55);rgb(64, 30, 21);rgb(55, 64, 21);rgb(21, 64, 30);rgb(21, 55, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 46, 54);rgb(25, 18, 54);rgb(54, 18, 46);rgb(54, 25, 18);rgb(46, 54, 18);rgb(18, 54, 25);rgb(18, 46, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(500 500.59)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 66, 77);rgb(36, 25, 77);rgb(77, 25, 66);rgb(77, 36, 25);rgb(66, 77, 25);rgb(25, 77, 36);rgb(25, 66, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 55, 64);rgb(30, 21, 64);rgb(64, 21, 55);rgb(64, 30, 21);rgb(55, 64, 21);rgb(21, 64, 30);rgb(21, 55, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 46, 54);rgb(25, 18, 54);rgb(54, 18, 46);rgb(54, 25, 18);rgb(46, 54, 18);rgb(18, 54, 25);rgb(18, 46, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(640 442.85)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 72, 77);rgb(30, 25, 77);rgb(77, 25, 72);rgb(77, 30, 25);rgb(72, 77, 25);rgb(25, 77, 30);rgb(25, 72, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 60, 64);rgb(25, 21, 64);rgb(64, 21, 60);rgb(64, 25, 21);rgb(60, 64, 21);rgb(21, 64, 25);rgb(21, 60, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 51, 54);rgb(21, 18, 54);rgb(54, 18, 51);rgb(54, 21, 18);rgb(51, 54, 18);rgb(18, 54, 21);rgb(18, 51, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(620 454.4)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 72, 77);rgb(30, 25, 77);rgb(77, 25, 72);rgb(77, 30, 25);rgb(72, 77, 25);rgb(25, 77, 30);rgb(25, 72, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 60, 64);rgb(25, 21, 64);rgb(64, 21, 60);rgb(64, 25, 21);rgb(60, 64, 21);rgb(21, 64, 25);rgb(21, 60, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 51, 54);rgb(21, 18, 54);rgb(54, 18, 51);rgb(54, 21, 18);rgb(51, 54, 18);rgb(18, 54, 21);rgb(18, 51, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(600 465.95)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 72, 77);rgb(30, 25, 77);rgb(77, 25, 72);rgb(77, 30, 25);rgb(72, 77, 25);rgb(25, 77, 30);rgb(25, 72, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 60, 64);rgb(25, 21, 64);rgb(64, 21, 60);rgb(64, 25, 21);rgb(60, 64, 21);rgb(21, 64, 25);rgb(21, 60, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 51, 54);rgb(21, 18, 54);rgb(54, 18, 51);rgb(54, 21, 18);rgb(51, 54, 18);rgb(18, 54, 21);rgb(18, 51, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(580 477.5)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 72, 77);rgb(30, 25, 77);rgb(77, 25, 72);rgb(77, 30, 25);rgb(72, 77, 25);rgb(25, 77, 30);rgb(25, 72, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 60, 64);rgb(25, 21, 64);rgb(64, 21, 60);rgb(64, 25, 21);rgb(60, 64, 21);rgb(21, 64, 25);rgb(21, 60, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 51, 54);rgb(21, 18, 54);rgb(54, 18, 51);rgb(54, 21, 18);rgb(51, 54, 18);rgb(18, 54, 21);rgb(18, 51, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(560 489.04)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 72, 77);rgb(30, 25, 77);rgb(77, 25, 72);rgb(77, 30, 25);rgb(72, 77, 25);rgb(25, 77, 30);rgb(25, 72, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 60, 64);rgb(25, 21, 64);rgb(64, 21, 60);rgb(64, 25, 21);rgb(60, 64, 21);rgb(21, 64, 25);rgb(21, 60, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 51, 54);rgb(21, 18, 54);rgb(54, 18, 51);rgb(54, 21, 18);rgb(51, 54, 18);rgb(18, 54, 21);rgb(18, 51, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(540 500.59)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 72, 77);rgb(30, 25, 77);rgb(77, 25, 72);rgb(77, 30, 25);rgb(72, 77, 25);rgb(25, 77, 30);rgb(25, 72, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 60, 64);rgb(25, 21, 64);rgb(64, 21, 60);rgb(64, 25, 21);rgb(60, 64, 21);rgb(21, 64, 25);rgb(21, 60, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 51, 54);rgb(21, 18, 54);rgb(54, 18, 51);rgb(54, 21, 18);rgb(51, 54, 18);rgb(18, 54, 21);rgb(18, 51, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(520 512.14)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 72, 77);rgb(30, 25, 77);rgb(77, 25, 72);rgb(77, 30, 25);rgb(72, 77, 25);rgb(25, 77, 30);rgb(25, 72, 77)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 60, 64);rgb(25, 21, 64);rgb(64, 21, 60);rgb(64, 25, 21);rgb(60, 64, 21);rgb(21, 64, 25);rgb(21, 60, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 51, 54);rgb(21, 18, 54);rgb(54, 18, 51);rgb(54, 21, 18);rgb(51, 54, 18);rgb(18, 54, 21);rgb(18, 51, 54)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(660 454.4)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 75);rgb(25, 27, 77);rgb(75, 25, 77);rgb(77, 25, 27);rgb(77, 75, 25);rgb(27, 77, 25);rgb(25, 77, 75)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 63);rgb(21, 23, 64);rgb(63, 21, 64);rgb(64, 21, 23);rgb(64, 63, 21);rgb(23, 64, 21);rgb(21, 64, 63)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 52);rgb(18, 19, 54);rgb(52, 18, 54);rgb(54, 18, 19);rgb(54, 52, 18);rgb(19, 54, 18);rgb(18, 54, 52)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(640 465.95)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 75);rgb(25, 27, 77);rgb(75, 25, 77);rgb(77, 25, 27);rgb(77, 75, 25);rgb(27, 77, 25);rgb(25, 77, 75)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 63);rgb(21, 23, 64);rgb(63, 21, 64);rgb(64, 21, 23);rgb(64, 63, 21);rgb(23, 64, 21);rgb(21, 64, 63)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 52);rgb(18, 19, 54);rgb(52, 18, 54);rgb(54, 18, 19);rgb(54, 52, 18);rgb(19, 54, 18);rgb(18, 54, 52)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(620 477.5)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 75);rgb(25, 27, 77);rgb(75, 25, 77);rgb(77, 25, 27);rgb(77, 75, 25);rgb(27, 77, 25);rgb(25, 77, 75)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 63);rgb(21, 23, 64);rgb(63, 21, 64);rgb(64, 21, 23);rgb(64, 63, 21);rgb(23, 64, 21);rgb(21, 64, 63)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 52);rgb(18, 19, 54);rgb(52, 18, 54);rgb(54, 18, 19);rgb(54, 52, 18);rgb(19, 54, 18);rgb(18, 54, 52)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(600 489.04)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 75);rgb(25, 27, 77);rgb(75, 25, 77);rgb(77, 25, 27);rgb(77, 75, 25);rgb(27, 77, 25);rgb(25, 77, 75)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 63);rgb(21, 23, 64);rgb(63, 21, 64);rgb(64, 21, 23);rgb(64, 63, 21);rgb(23, 64, 21);rgb(21, 64, 63)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 52);rgb(18, 19, 54);rgb(52, 18, 54);rgb(54, 18, 19);rgb(54, 52, 18);rgb(19, 54, 18);rgb(18, 54, 52)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(580 500.59)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 75);rgb(25, 27, 77);rgb(75, 25, 77);rgb(77, 25, 27);rgb(77, 75, 25);rgb(27, 77, 25);rgb(25, 77, 75)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 63);rgb(21, 23, 64);rgb(63, 21, 64);rgb(64, 21, 23);rgb(64, 63, 21);rgb(23, 64, 21);rgb(21, 64, 63)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 52);rgb(18, 19, 54);rgb(52, 18, 54);rgb(54, 18, 19);rgb(54, 52, 18);rgb(19, 54, 18);rgb(18, 54, 52)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(560 512.14)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 75);rgb(25, 27, 77);rgb(75, 25, 77);rgb(77, 25, 27);rgb(77, 75, 25);rgb(27, 77, 25);rgb(25, 77, 75)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 63);rgb(21, 23, 64);rgb(63, 21, 64);rgb(64, 21, 23);rgb(64, 63, 21);rgb(23, 64, 21);rgb(21, 64, 63)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 52);rgb(18, 19, 54);rgb(52, 18, 54);rgb(54, 18, 19);rgb(54, 52, 18);rgb(19, 54, 18);rgb(18, 54, 52)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(540 523.68)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 75);rgb(25, 27, 77);rgb(75, 25, 77);rgb(77, 25, 27);rgb(77, 75, 25);rgb(27, 77, 25);rgb(25, 77, 75)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 63);rgb(21, 23, 64);rgb(63, 21, 64);rgb(64, 21, 23);rgb(64, 63, 21);rgb(23, 64, 21);rgb(21, 64, 63)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 52);rgb(18, 19, 54);rgb(52, 18, 54);rgb(54, 18, 19);rgb(54, 52, 18);rgb(19, 54, 18);rgb(18, 54, 52)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(680 465.95)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 69);rgb(25, 33, 77);rgb(69, 25, 77);rgb(77, 25, 33);rgb(77, 69, 25);rgb(33, 77, 25);rgb(25, 77, 69)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 58);rgb(21, 28, 64);rgb(58, 21, 64);rgb(64, 21, 28);rgb(64, 58, 21);rgb(28, 64, 21);rgb(21, 64, 58)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 48);rgb(18, 23, 54);rgb(48, 18, 54);rgb(54, 18, 23);rgb(54, 48, 18);rgb(23, 54, 18);rgb(18, 54, 48)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(660 477.5)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 69);rgb(25, 33, 77);rgb(69, 25, 77);rgb(77, 25, 33);rgb(77, 69, 25);rgb(33, 77, 25);rgb(25, 77, 69)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 58);rgb(21, 28, 64);rgb(58, 21, 64);rgb(64, 21, 28);rgb(64, 58, 21);rgb(28, 64, 21);rgb(21, 64, 58)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 48);rgb(18, 23, 54);rgb(48, 18, 54);rgb(54, 18, 23);rgb(54, 48, 18);rgb(23, 54, 18);rgb(18, 54, 48)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(640 489.04)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 69);rgb(25, 33, 77);rgb(69, 25, 77);rgb(77, 25, 33);rgb(77, 69, 25);rgb(33, 77, 25);rgb(25, 77, 69)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 58);rgb(21, 28, 64);rgb(58, 21, 64);rgb(64, 21, 28);rgb(64, 58, 21);rgb(28, 64, 21);rgb(21, 64, 58)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 48);rgb(18, 23, 54);rgb(48, 18, 54);rgb(54, 18, 23);rgb(54, 48, 18);rgb(23, 54, 18);rgb(18, 54, 48)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(620 500.59)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 69);rgb(25, 33, 77);rgb(69, 25, 77);rgb(77, 25, 33);rgb(77, 69, 25);rgb(33, 77, 25);rgb(25, 77, 69)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 58);rgb(21, 28, 64);rgb(58, 21, 64);rgb(64, 21, 28);rgb(64, 58, 21);rgb(28, 64, 21);rgb(21, 64, 58)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 48);rgb(18, 23, 54);rgb(48, 18, 54);rgb(54, 18, 23);rgb(54, 48, 18);rgb(23, 54, 18);rgb(18, 54, 48)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(600 512.14)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 69);rgb(25, 33, 77);rgb(69, 25, 77);rgb(77, 25, 33);rgb(77, 69, 25);rgb(33, 77, 25);rgb(25, 77, 69)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 58);rgb(21, 28, 64);rgb(58, 21, 64);rgb(64, 21, 28);rgb(64, 58, 21);rgb(28, 64, 21);rgb(21, 64, 58)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 48);rgb(18, 23, 54);rgb(48, 18, 54);rgb(54, 18, 23);rgb(54, 48, 18);rgb(23, 54, 18);rgb(18, 54, 48)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(580 523.68)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 69);rgb(25, 33, 77);rgb(69, 25, 77);rgb(77, 25, 33);rgb(77, 69, 25);rgb(33, 77, 25);rgb(25, 77, 69)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 58);rgb(21, 28, 64);rgb(58, 21, 64);rgb(64, 21, 28);rgb(64, 58, 21);rgb(28, 64, 21);rgb(21, 64, 58)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 48);rgb(18, 23, 54);rgb(48, 18, 54);rgb(54, 18, 23);rgb(54, 48, 18);rgb(23, 54, 18);rgb(18, 54, 48)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(560 535.23)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 69);rgb(25, 33, 77);rgb(69, 25, 77);rgb(77, 25, 33);rgb(77, 69, 25);rgb(33, 77, 25);rgb(25, 77, 69)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 58);rgb(21, 28, 64);rgb(58, 21, 64);rgb(64, 21, 28);rgb(64, 58, 21);rgb(28, 64, 21);rgb(21, 64, 58)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 48);rgb(18, 23, 54);rgb(48, 18, 54);rgb(54, 18, 23);rgb(54, 48, 18);rgb(23, 54, 18);rgb(18, 54, 48)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(700 477.5)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 63);rgb(25, 39, 77);rgb(63, 25, 77);rgb(77, 25, 39);rgb(77, 63, 25);rgb(39, 77, 25);rgb(25, 77, 63)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 53);rgb(21, 33, 64);rgb(53, 21, 64);rgb(64, 21, 33);rgb(64, 53, 21);rgb(33, 64, 21);rgb(21, 64, 53)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 44);rgb(18, 27, 54);rgb(44, 18, 54);rgb(54, 18, 27);rgb(54, 44, 18);rgb(27, 54, 18);rgb(18, 54, 44)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(680 489.04)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 63);rgb(25, 39, 77);rgb(63, 25, 77);rgb(77, 25, 39);rgb(77, 63, 25);rgb(39, 77, 25);rgb(25, 77, 63)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 53);rgb(21, 33, 64);rgb(53, 21, 64);rgb(64, 21, 33);rgb(64, 53, 21);rgb(33, 64, 21);rgb(21, 64, 53)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 44);rgb(18, 27, 54);rgb(44, 18, 54);rgb(54, 18, 27);rgb(54, 44, 18);rgb(27, 54, 18);rgb(18, 54, 44)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(660 500.59)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 63);rgb(25, 39, 77);rgb(63, 25, 77);rgb(77, 25, 39);rgb(77, 63, 25);rgb(39, 77, 25);rgb(25, 77, 63)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 53);rgb(21, 33, 64);rgb(53, 21, 64);rgb(64, 21, 33);rgb(64, 53, 21);rgb(33, 64, 21);rgb(21, 64, 53)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 44);rgb(18, 27, 54);rgb(44, 18, 54);rgb(54, 18, 27);rgb(54, 44, 18);rgb(27, 54, 18);rgb(18, 54, 44)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(640 512.14)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 63);rgb(25, 39, 77);rgb(63, 25, 77);rgb(77, 25, 39);rgb(77, 63, 25);rgb(39, 77, 25);rgb(25, 77, 63)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 53);rgb(21, 33, 64);rgb(53, 21, 64);rgb(64, 21, 33);rgb(64, 53, 21);rgb(33, 64, 21);rgb(21, 64, 53)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 44);rgb(18, 27, 54);rgb(44, 18, 54);rgb(54, 18, 27);rgb(54, 44, 18);rgb(27, 54, 18);rgb(18, 54, 44)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(620 523.68)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 63);rgb(25, 39, 77);rgb(63, 25, 77);rgb(77, 25, 39);rgb(77, 63, 25);rgb(39, 77, 25);rgb(25, 77, 63)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 53);rgb(21, 33, 64);rgb(53, 21, 64);rgb(64, 21, 33);rgb(64, 53, 21);rgb(33, 64, 21);rgb(21, 64, 53)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 44);rgb(18, 27, 54);rgb(44, 18, 54);rgb(54, 18, 27);rgb(54, 44, 18);rgb(27, 54, 18);rgb(18, 54, 44)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(600 535.23)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 63);rgb(25, 39, 77);rgb(63, 25, 77);rgb(77, 25, 39);rgb(77, 63, 25);rgb(39, 77, 25);rgb(25, 77, 63)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 53);rgb(21, 33, 64);rgb(53, 21, 64);rgb(64, 21, 33);rgb(64, 53, 21);rgb(33, 64, 21);rgb(21, 64, 53)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 44);rgb(18, 27, 54);rgb(44, 18, 54);rgb(54, 18, 27);rgb(54, 44, 18);rgb(27, 54, 18);rgb(18, 54, 44)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(580 546.78)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 63);rgb(25, 39, 77);rgb(63, 25, 77);rgb(77, 25, 39);rgb(77, 63, 25);rgb(39, 77, 25);rgb(25, 77, 63)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 53);rgb(21, 33, 64);rgb(53, 21, 64);rgb(64, 21, 33);rgb(64, 53, 21);rgb(33, 64, 21);rgb(21, 64, 53)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 44);rgb(18, 27, 54);rgb(44, 18, 54);rgb(54, 18, 27);rgb(54, 44, 18);rgb(27, 54, 18);rgb(18, 54, 44)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(720 489.04)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 57);rgb(25, 45, 77);rgb(57, 25, 77);rgb(77, 25, 45);rgb(77, 57, 25);rgb(45, 77, 25);rgb(25, 77, 57)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 48);rgb(21, 38, 64);rgb(48, 21, 64);rgb(64, 21, 38);rgb(64, 48, 21);rgb(38, 64, 21);rgb(21, 64, 48)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 40);rgb(18, 32, 54);rgb(40, 18, 54);rgb(54, 18, 32);rgb(54, 40, 18);rgb(32, 54, 18);rgb(18, 54, 40)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(700 500.59)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 57);rgb(25, 45, 77);rgb(57, 25, 77);rgb(77, 25, 45);rgb(77, 57, 25);rgb(45, 77, 25);rgb(25, 77, 57)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 48);rgb(21, 38, 64);rgb(48, 21, 64);rgb(64, 21, 38);rgb(64, 48, 21);rgb(38, 64, 21);rgb(21, 64, 48)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 40);rgb(18, 32, 54);rgb(40, 18, 54);rgb(54, 18, 32);rgb(54, 40, 18);rgb(32, 54, 18);rgb(18, 54, 40)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(680 512.14)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 57);rgb(25, 45, 77);rgb(57, 25, 77);rgb(77, 25, 45);rgb(77, 57, 25);rgb(45, 77, 25);rgb(25, 77, 57)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 48);rgb(21, 38, 64);rgb(48, 21, 64);rgb(64, 21, 38);rgb(64, 48, 21);rgb(38, 64, 21);rgb(21, 64, 48)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 40);rgb(18, 32, 54);rgb(40, 18, 54);rgb(54, 18, 32);rgb(54, 40, 18);rgb(32, 54, 18);rgb(18, 54, 40)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(660 523.68)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 57);rgb(25, 45, 77);rgb(57, 25, 77);rgb(77, 25, 45);rgb(77, 57, 25);rgb(45, 77, 25);rgb(25, 77, 57)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 48);rgb(21, 38, 64);rgb(48, 21, 64);rgb(64, 21, 38);rgb(64, 48, 21);rgb(38, 64, 21);rgb(21, 64, 48)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 40);rgb(18, 32, 54);rgb(40, 18, 54);rgb(54, 18, 32);rgb(54, 40, 18);rgb(32, 54, 18);rgb(18, 54, 40)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(640 535.23)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 57);rgb(25, 45, 77);rgb(57, 25, 77);rgb(77, 25, 45);rgb(77, 57, 25);rgb(45, 77, 25);rgb(25, 77, 57)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 48);rgb(21, 38, 64);rgb(48, 21, 64);rgb(64, 21, 38);rgb(64, 48, 21);rgb(38, 64, 21);rgb(21, 64, 48)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 40);rgb(18, 32, 54);rgb(40, 18, 54);rgb(54, 18, 32);rgb(54, 40, 18);rgb(32, 54, 18);rgb(18, 54, 40)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(620 546.78)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 57);rgb(25, 45, 77);rgb(57, 25, 77);rgb(77, 25, 45);rgb(77, 57, 25);rgb(45, 77, 25);rgb(25, 77, 57)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 48);rgb(21, 38, 64);rgb(48, 21, 64);rgb(64, 21, 38);rgb(64, 48, 21);rgb(38, 64, 21);rgb(21, 64, 48)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 40);rgb(18, 32, 54);rgb(40, 18, 54);rgb(54, 18, 32);rgb(54, 40, 18);rgb(32, 54, 18);rgb(18, 54, 40)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(600 558.32)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 57);rgb(25, 45, 77);rgb(57, 25, 77);rgb(77, 25, 45);rgb(77, 57, 25);rgb(45, 77, 25);rgb(25, 77, 57)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 48);rgb(21, 38, 64);rgb(48, 21, 64);rgb(64, 21, 38);rgb(64, 48, 21);rgb(38, 64, 21);rgb(21, 64, 48)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 40);rgb(18, 32, 54);rgb(40, 18, 54);rgb(54, 18, 32);rgb(54, 40, 18);rgb(32, 54, 18);rgb(18, 54, 40)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(740 500.59)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 51);rgb(25, 51, 77);rgb(51, 25, 77);rgb(77, 25, 51);rgb(77, 51, 25);rgb(51, 77, 25);rgb(25, 77, 51)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 43);rgb(21, 43, 64);rgb(43, 21, 64);rgb(64, 21, 43);rgb(64, 43, 21);rgb(43, 64, 21);rgb(21, 64, 43)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 36);rgb(18, 36, 54);rgb(36, 18, 54);rgb(54, 18, 36);rgb(54, 36, 18);rgb(36, 54, 18);rgb(18, 54, 36)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(720 512.14)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 51);rgb(25, 51, 77);rgb(51, 25, 77);rgb(77, 25, 51);rgb(77, 51, 25);rgb(51, 77, 25);rgb(25, 77, 51)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 43);rgb(21, 43, 64);rgb(43, 21, 64);rgb(64, 21, 43);rgb(64, 43, 21);rgb(43, 64, 21);rgb(21, 64, 43)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 36);rgb(18, 36, 54);rgb(36, 18, 54);rgb(54, 18, 36);rgb(54, 36, 18);rgb(36, 54, 18);rgb(18, 54, 36)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(700 523.68)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 51);rgb(25, 51, 77);rgb(51, 25, 77);rgb(77, 25, 51);rgb(77, 51, 25);rgb(51, 77, 25);rgb(25, 77, 51)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 43);rgb(21, 43, 64);rgb(43, 21, 64);rgb(64, 21, 43);rgb(64, 43, 21);rgb(43, 64, 21);rgb(21, 64, 43)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 36);rgb(18, 36, 54);rgb(36, 18, 54);rgb(54, 18, 36);rgb(54, 36, 18);rgb(36, 54, 18);rgb(18, 54, 36)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(680 535.23)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 51);rgb(25, 51, 77);rgb(51, 25, 77);rgb(77, 25, 51);rgb(77, 51, 25);rgb(51, 77, 25);rgb(25, 77, 51)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 43);rgb(21, 43, 64);rgb(43, 21, 64);rgb(64, 21, 43);rgb(64, 43, 21);rgb(43, 64, 21);rgb(21, 64, 43)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 36);rgb(18, 36, 54);rgb(36, 18, 54);rgb(54, 18, 36);rgb(54, 36, 18);rgb(36, 54, 18);rgb(18, 54, 36)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(660 546.78)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 51);rgb(25, 51, 77);rgb(51, 25, 77);rgb(77, 25, 51);rgb(77, 51, 25);rgb(51, 77, 25);rgb(25, 77, 51)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 43);rgb(21, 43, 64);rgb(43, 21, 64);rgb(64, 21, 43);rgb(64, 43, 21);rgb(43, 64, 21);rgb(21, 64, 43)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 36);rgb(18, 36, 54);rgb(36, 18, 54);rgb(54, 18, 36);rgb(54, 36, 18);rgb(36, 54, 18);rgb(18, 54, 36)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(640 558.32)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 51);rgb(25, 51, 77);rgb(51, 25, 77);rgb(77, 25, 51);rgb(77, 51, 25);rgb(51, 77, 25);rgb(25, 77, 51)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 43);rgb(21, 43, 64);rgb(43, 21, 64);rgb(64, 21, 43);rgb(64, 43, 21);rgb(43, 64, 21);rgb(21, 64, 43)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 36);rgb(18, 36, 54);rgb(36, 18, 54);rgb(54, 18, 36);rgb(54, 36, 18);rgb(36, 54, 18);rgb(18, 54, 36)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(620 569.87)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 51);rgb(25, 51, 77);rgb(51, 25, 77);rgb(77, 25, 51);rgb(77, 51, 25);rgb(51, 77, 25);rgb(25, 77, 51)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 43);rgb(21, 43, 64);rgb(43, 21, 64);rgb(64, 21, 43);rgb(64, 43, 21);rgb(43, 64, 21);rgb(21, 64, 43)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 36);rgb(18, 36, 54);rgb(36, 18, 54);rgb(54, 18, 36);rgb(54, 36, 18);rgb(36, 54, 18);rgb(18, 54, 36)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(760 512.14)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 45);rgb(25, 57, 77);rgb(45, 25, 77);rgb(77, 25, 57);rgb(77, 45, 25);rgb(57, 77, 25);rgb(25, 77, 45)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 38);rgb(21, 48, 64);rgb(38, 21, 64);rgb(64, 21, 48);rgb(64, 38, 21);rgb(48, 64, 21);rgb(21, 64, 38)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 32);rgb(18, 40, 54);rgb(32, 18, 54);rgb(54, 18, 40);rgb(54, 32, 18);rgb(40, 54, 18);rgb(18, 54, 32)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(740 523.68)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 45);rgb(25, 57, 77);rgb(45, 25, 77);rgb(77, 25, 57);rgb(77, 45, 25);rgb(57, 77, 25);rgb(25, 77, 45)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 38);rgb(21, 48, 64);rgb(38, 21, 64);rgb(64, 21, 48);rgb(64, 38, 21);rgb(48, 64, 21);rgb(21, 64, 38)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 32);rgb(18, 40, 54);rgb(32, 18, 54);rgb(54, 18, 40);rgb(54, 32, 18);rgb(40, 54, 18);rgb(18, 54, 32)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(720 535.23)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 45);rgb(25, 57, 77);rgb(45, 25, 77);rgb(77, 25, 57);rgb(77, 45, 25);rgb(57, 77, 25);rgb(25, 77, 45)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 38);rgb(21, 48, 64);rgb(38, 21, 64);rgb(64, 21, 48);rgb(64, 38, 21);rgb(48, 64, 21);rgb(21, 64, 38)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 32);rgb(18, 40, 54);rgb(32, 18, 54);rgb(54, 18, 40);rgb(54, 32, 18);rgb(40, 54, 18);rgb(18, 54, 32)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(700 546.78)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 45);rgb(25, 57, 77);rgb(45, 25, 77);rgb(77, 25, 57);rgb(77, 45, 25);rgb(57, 77, 25);rgb(25, 77, 45)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 38);rgb(21, 48, 64);rgb(38, 21, 64);rgb(64, 21, 48);rgb(64, 38, 21);rgb(48, 64, 21);rgb(21, 64, 38)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 32);rgb(18, 40, 54);rgb(32, 18, 54);rgb(54, 18, 40);rgb(54, 32, 18);rgb(40, 54, 18);rgb(18, 54, 32)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(680 558.32)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 45);rgb(25, 57, 77);rgb(45, 25, 77);rgb(77, 25, 57);rgb(77, 45, 25);rgb(57, 77, 25);rgb(25, 77, 45)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 38);rgb(21, 48, 64);rgb(38, 21, 64);rgb(64, 21, 48);rgb(64, 38, 21);rgb(48, 64, 21);rgb(21, 64, 38)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 32);rgb(18, 40, 54);rgb(32, 18, 54);rgb(54, 18, 40);rgb(54, 32, 18);rgb(40, 54, 18);rgb(18, 54, 32)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(660 569.87)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 45);rgb(25, 57, 77);rgb(45, 25, 77);rgb(77, 25, 57);rgb(77, 45, 25);rgb(57, 77, 25);rgb(25, 77, 45)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 38);rgb(21, 48, 64);rgb(38, 21, 64);rgb(64, 21, 48);rgb(64, 38, 21);rgb(48, 64, 21);rgb(21, 64, 38)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 32);rgb(18, 40, 54);rgb(32, 18, 54);rgb(54, 18, 40);rgb(54, 32, 18);rgb(40, 54, 18);rgb(18, 54, 32)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(640 581.42)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 45);rgb(25, 57, 77);rgb(45, 25, 77);rgb(77, 25, 57);rgb(77, 45, 25);rgb(57, 77, 25);rgb(25, 77, 45)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 38);rgb(21, 48, 64);rgb(38, 21, 64);rgb(64, 21, 48);rgb(64, 38, 21);rgb(48, 64, 21);rgb(21, 64, 38)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 32);rgb(18, 40, 54);rgb(32, 18, 54);rgb(54, 18, 40);rgb(54, 32, 18);rgb(40, 54, 18);rgb(18, 54, 32)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(780 523.68)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 39);rgb(25, 63, 77);rgb(39, 25, 77);rgb(77, 25, 63);rgb(77, 39, 25);rgb(63, 77, 25);rgb(25, 77, 39)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 33);rgb(21, 53, 64);rgb(33, 21, 64);rgb(64, 21, 53);rgb(64, 33, 21);rgb(53, 64, 21);rgb(21, 64, 33)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 27);rgb(18, 44, 54);rgb(27, 18, 54);rgb(54, 18, 44);rgb(54, 27, 18);rgb(44, 54, 18);rgb(18, 54, 27)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(760 535.23)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 39);rgb(25, 63, 77);rgb(39, 25, 77);rgb(77, 25, 63);rgb(77, 39, 25);rgb(63, 77, 25);rgb(25, 77, 39)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 33);rgb(21, 53, 64);rgb(33, 21, 64);rgb(64, 21, 53);rgb(64, 33, 21);rgb(53, 64, 21);rgb(21, 64, 33)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 27);rgb(18, 44, 54);rgb(27, 18, 54);rgb(54, 18, 44);rgb(54, 27, 18);rgb(44, 54, 18);rgb(18, 54, 27)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(740 546.78)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 39);rgb(25, 63, 77);rgb(39, 25, 77);rgb(77, 25, 63);rgb(77, 39, 25);rgb(63, 77, 25);rgb(25, 77, 39)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 33);rgb(21, 53, 64);rgb(33, 21, 64);rgb(64, 21, 53);rgb(64, 33, 21);rgb(53, 64, 21);rgb(21, 64, 33)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 27);rgb(18, 44, 54);rgb(27, 18, 54);rgb(54, 18, 44);rgb(54, 27, 18);rgb(44, 54, 18);rgb(18, 54, 27)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(720 558.32)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 39);rgb(25, 63, 77);rgb(39, 25, 77);rgb(77, 25, 63);rgb(77, 39, 25);rgb(63, 77, 25);rgb(25, 77, 39)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 33);rgb(21, 53, 64);rgb(33, 21, 64);rgb(64, 21, 53);rgb(64, 33, 21);rgb(53, 64, 21);rgb(21, 64, 33)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 27);rgb(18, 44, 54);rgb(27, 18, 54);rgb(54, 18, 44);rgb(54, 27, 18);rgb(44, 54, 18);rgb(18, 54, 27)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(700 569.87)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 39);rgb(25, 63, 77);rgb(39, 25, 77);rgb(77, 25, 63);rgb(77, 39, 25);rgb(63, 77, 25);rgb(25, 77, 39)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 33);rgb(21, 53, 64);rgb(33, 21, 64);rgb(64, 21, 53);rgb(64, 33, 21);rgb(53, 64, 21);rgb(21, 64, 33)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 27);rgb(18, 44, 54);rgb(27, 18, 54);rgb(54, 18, 44);rgb(54, 27, 18);rgb(44, 54, 18);rgb(18, 54, 27)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(680 581.42)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 39);rgb(25, 63, 77);rgb(39, 25, 77);rgb(77, 25, 63);rgb(77, 39, 25);rgb(63, 77, 25);rgb(25, 77, 39)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 33);rgb(21, 53, 64);rgb(33, 21, 64);rgb(64, 21, 53);rgb(64, 33, 21);rgb(53, 64, 21);rgb(21, 64, 33)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 27);rgb(18, 44, 54);rgb(27, 18, 54);rgb(54, 18, 44);rgb(54, 27, 18);rgb(44, 54, 18);rgb(18, 54, 27)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(660 592.97)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 39);rgb(25, 63, 77);rgb(39, 25, 77);rgb(77, 25, 63);rgb(77, 39, 25);rgb(63, 77, 25);rgb(25, 77, 39)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 33);rgb(21, 53, 64);rgb(33, 21, 64);rgb(64, 21, 53);rgb(64, 33, 21);rgb(53, 64, 21);rgb(21, 64, 33)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 27);rgb(18, 44, 54);rgb(27, 18, 54);rgb(54, 18, 44);rgb(54, 27, 18);rgb(44, 54, 18);rgb(18, 54, 27)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(800 535.23)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 33);rgb(25, 69, 77);rgb(33, 25, 77);rgb(77, 25, 69);rgb(77, 33, 25);rgb(69, 77, 25);rgb(25, 77, 33)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 28);rgb(21, 58, 64);rgb(28, 21, 64);rgb(64, 21, 58);rgb(64, 28, 21);rgb(58, 64, 21);rgb(21, 64, 28)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 23);rgb(18, 48, 54);rgb(23, 18, 54);rgb(54, 18, 48);rgb(54, 23, 18);rgb(48, 54, 18);rgb(18, 54, 23)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(780 546.78)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 33);rgb(25, 69, 77);rgb(33, 25, 77);rgb(77, 25, 69);rgb(77, 33, 25);rgb(69, 77, 25);rgb(25, 77, 33)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 28);rgb(21, 58, 64);rgb(28, 21, 64);rgb(64, 21, 58);rgb(64, 28, 21);rgb(58, 64, 21);rgb(21, 64, 28)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 23);rgb(18, 48, 54);rgb(23, 18, 54);rgb(54, 18, 48);rgb(54, 23, 18);rgb(48, 54, 18);rgb(18, 54, 23)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(760 558.32)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 33);rgb(25, 69, 77);rgb(33, 25, 77);rgb(77, 25, 69);rgb(77, 33, 25);rgb(69, 77, 25);rgb(25, 77, 33)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 28);rgb(21, 58, 64);rgb(28, 21, 64);rgb(64, 21, 58);rgb(64, 28, 21);rgb(58, 64, 21);rgb(21, 64, 28)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 23);rgb(18, 48, 54);rgb(23, 18, 54);rgb(54, 18, 48);rgb(54, 23, 18);rgb(48, 54, 18);rgb(18, 54, 23)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(740 569.87)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 33);rgb(25, 69, 77);rgb(33, 25, 77);rgb(77, 25, 69);rgb(77, 33, 25);rgb(69, 77, 25);rgb(25, 77, 33)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 28);rgb(21, 58, 64);rgb(28, 21, 64);rgb(64, 21, 58);rgb(64, 28, 21);rgb(58, 64, 21);rgb(21, 64, 28)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 23);rgb(18, 48, 54);rgb(23, 18, 54);rgb(54, 18, 48);rgb(54, 23, 18);rgb(48, 54, 18);rgb(18, 54, 23)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(720 581.42)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 33);rgb(25, 69, 77);rgb(33, 25, 77);rgb(77, 25, 69);rgb(77, 33, 25);rgb(69, 77, 25);rgb(25, 77, 33)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 28);rgb(21, 58, 64);rgb(28, 21, 64);rgb(64, 21, 58);rgb(64, 28, 21);rgb(58, 64, 21);rgb(21, 64, 28)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 23);rgb(18, 48, 54);rgb(23, 18, 54);rgb(54, 18, 48);rgb(54, 23, 18);rgb(48, 54, 18);rgb(18, 54, 23)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(700 587.01)"><animateTransform attributeName="transform" type="translate" values="700 592.97;700 587.01" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(38, 115, 50);rgb(38, 103, 115);rgb(50, 38, 115);rgb(115, 38, 103);rgb(115, 50, 38);rgb(103, 115, 38);rgb(38, 115, 50)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="7.76" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(32, 96, 42);rgb(32, 86, 96);rgb(42, 32, 96);rgb(96, 32, 86);rgb(96, 42, 32);rgb(86, 96, 32);rgb(32, 96, 42)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;7.76" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="7.76" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(27, 80, 35);rgb(27, 72, 80);rgb(35, 27, 80);rgb(80, 27, 72);rgb(80, 35, 27);rgb(72, 80, 27);rgb(27, 80, 35)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;7.76" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(680 601.46)"><animateTransform attributeName="transform" type="translate" values="680 604.51;680 601.46" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(38, 115, 50);rgb(38, 103, 115);rgb(50, 38, 115);rgb(115, 38, 103);rgb(115, 50, 38);rgb(103, 115, 38);rgb(38, 115, 50)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="5.24" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(32, 96, 42);rgb(32, 86, 96);rgb(42, 32, 96);rgb(96, 32, 86);rgb(96, 42, 32);rgb(86, 96, 32);rgb(32, 96, 42)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;5.24" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="5.24" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(27, 80, 35);rgb(27, 72, 80);rgb(35, 27, 80);rgb(80, 27, 72);rgb(80, 35, 27);rgb(72, 80, 27);rgb(27, 80, 35)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;5.24" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(820 546.78)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 27);rgb(25, 75, 77);rgb(27, 25, 77);rgb(77, 25, 75);rgb(77, 27, 25);rgb(75, 77, 25);rgb(25, 77, 27)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 23);rgb(21, 63, 64);rgb(23, 21, 64);rgb(64, 21, 63);rgb(64, 23, 21);rgb(63, 64, 21);rgb(21, 64, 23)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 19);rgb(18, 52, 54);rgb(19, 18, 54);rgb(54, 18, 52);rgb(54, 19, 18);rgb(52, 54, 18);rgb(18, 54, 19)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(800 558.32)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 27);rgb(25, 75, 77);rgb(27, 25, 77);rgb(77, 25, 75);rgb(77, 27, 25);rgb(75, 77, 25);rgb(25, 77, 27)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 23);rgb(21, 63, 64);rgb(23, 21, 64);rgb(64, 21, 63);rgb(64, 23, 21);rgb(63, 64, 21);rgb(21, 64, 23)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 19);rgb(18, 52, 54);rgb(19, 18, 54);rgb(54, 18, 52);rgb(54, 19, 18);rgb(52, 54, 18);rgb(18, 54, 19)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(780 569.87)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 27);rgb(25, 75, 77);rgb(27, 25, 77);rgb(77, 25, 75);rgb(77, 27, 25);rgb(75, 77, 25);rgb(25, 77, 27)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 23);rgb(21, 63, 64);rgb(23, 21, 64);rgb(64, 21, 63);rgb(64, 23, 21);rgb(63, 64, 21);rgb(21, 64, 23)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 19);rgb(18, 52, 54);rgb(19, 18, 54);rgb(54, 18, 52);rgb(54, 19, 18);rgb(52, 54, 18);rgb(18, 54, 19)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(760 578.37)"><animateTransform attributeName="transform" type="translate" values="760 581.42;760 578.37" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(38, 115, 41);rgb(38, 112, 115);rgb(41, 38, 115);rgb(115, 38, 112);rgb(115, 41, 38);rgb(112, 115, 38);rgb(38, 115, 41)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="5.24" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(32, 96, 34);rgb(32, 94, 96);rgb(34, 32, 96);rgb(96, 32, 94);rgb(96, 34, 32);rgb(94, 96, 32);rgb(32, 96, 34)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;5.24" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="5.24" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(27, 80, 29);rgb(27, 79, 80);rgb(29, 27, 80);rgb(80, 27, 79);rgb(80, 29, 27);rgb(79, 80, 27);rgb(27, 80, 29)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;5.24" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(740 592.97)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 27);rgb(25, 75, 77);rgb(27, 25, 77);rgb(77, 25, 75);rgb(77, 27, 25);rgb(75, 77, 25);rgb(25, 77, 27)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 23);rgb(21, 63, 64);rgb(23, 21, 64);rgb(64, 21, 63);rgb(64, 23, 21);rgb(63, 64, 21);rgb(21, 64, 23)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 19);rgb(18, 52, 54);rgb(19, 18, 54);rgb(54, 18, 52);rgb(54, 19, 18);rgb(52, 54, 18);rgb(18, 54, 19)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(720 604.51)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 27);rgb(25, 75, 77);rgb(27, 25, 77);rgb(77, 25, 75);rgb(77, 27, 25);rgb(75, 77, 25);rgb(25, 77, 27)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 23);rgb(21, 63, 64);rgb(23, 21, 64);rgb(64, 21, 63);rgb(64, 23, 21);rgb(63, 64, 21);rgb(21, 64, 23)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 19);rgb(18, 52, 54);rgb(19, 18, 54);rgb(54, 18, 52);rgb(54, 19, 18);rgb(52, 54, 18);rgb(18, 54, 19)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(700 616.06)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 77, 27);rgb(25, 75, 77);rgb(27, 25, 77);rgb(77, 25, 75);rgb(77, 27, 25);rgb(75, 77, 25);rgb(25, 77, 27)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(21, 64, 23);rgb(21, 63, 64);rgb(23, 21, 64);rgb(64, 21, 63);rgb(64, 23, 21);rgb(63, 64, 21);rgb(21, 64, 23)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(18, 54, 19);rgb(18, 52, 54);rgb(19, 18, 54);rgb(54, 18, 52);rgb(54, 19, 18);rgb(52, 54, 18);rgb(18, 54, 19)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(840 535.09)"><animateTransform attributeName="transform" type="translate" values="840 558.32;840 535.09" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(52, 134, 45);rgb(45, 134, 126);rgb(45, 52, 134);rgb(126, 45, 134);rgb(134, 45, 52);rgb(134, 126, 45);rgb(52, 134, 45)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="22.72" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(44, 112, 37);rgb(37, 112, 106);rgb(37, 44, 112);rgb(106, 37, 112);rgb(112, 37, 44);rgb(112, 106, 37);rgb(44, 112, 37)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;22.72" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="22.72" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(36, 94, 31);rgb(31, 94, 89);rgb(31, 36, 94);rgb(89, 31, 94);rgb(94, 31, 36);rgb(94, 89, 31);rgb(36, 94, 31)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;22.72" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(820 563.91)"><animateTransform attributeName="transform" type="translate" values="820 569.87;820 563.91" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(45, 115, 38);rgb(38, 115, 108);rgb(38, 45, 115);rgb(108, 38, 115);rgb(115, 38, 45);rgb(115, 108, 38);rgb(45, 115, 38)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="7.76" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(37, 96, 32);rgb(32, 96, 91);rgb(32, 37, 96);rgb(91, 32, 96);rgb(96, 32, 37);rgb(96, 91, 32);rgb(37, 96, 32)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;7.76" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="7.76" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(31, 80, 27);rgb(27, 80, 76);rgb(27, 31, 80);rgb(76, 27, 80);rgb(80, 27, 31);rgb(80, 76, 27);rgb(31, 80, 27)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;7.76" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(800 578.37)"><animateTransform attributeName="transform" type="translate" values="800 581.42;800 578.37" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(45, 115, 38);rgb(38, 115, 108);rgb(38, 45, 115);rgb(108, 38, 115);rgb(115, 38, 45);rgb(115, 108, 38);rgb(45, 115, 38)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="5.24" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(37, 96, 32);rgb(32, 96, 91);rgb(32, 37, 96);rgb(91, 32, 96);rgb(96, 32, 37);rgb(96, 91, 32);rgb(37, 96, 32)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;5.24" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="5.24" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(31, 80, 27);rgb(27, 80, 76);rgb(27, 31, 80);rgb(76, 27, 80);rgb(80, 27, 31);rgb(80, 76, 27);rgb(31, 80, 27)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;5.24" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(780 563.57)"><animateTransform attributeName="transform" type="translate" values="780 592.97;780 563.57" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(52, 134, 45);rgb(45, 134, 126);rgb(45, 52, 134);rgb(126, 45, 134);rgb(134, 45, 52);rgb(134, 126, 45);rgb(52, 134, 45)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="28.05" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(44, 112, 37);rgb(37, 112, 106);rgb(37, 44, 112);rgb(106, 37, 112);rgb(112, 37, 44);rgb(112, 106, 37);rgb(44, 112, 37)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;28.05" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="28.05" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(36, 94, 31);rgb(31, 94, 89);rgb(31, 36, 94);rgb(89, 31, 94);rgb(94, 31, 36);rgb(94, 89, 31);rgb(36, 94, 31)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;28.05" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(760 601.46)"><animateTransform attributeName="transform" type="translate" values="760 604.51;760 601.46" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(45, 115, 38);rgb(38, 115, 108);rgb(38, 45, 115);rgb(108, 38, 115);rgb(115, 38, 45);rgb(115, 108, 38);rgb(45, 115, 38)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="5.24" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(37, 96, 32);rgb(32, 96, 91);rgb(32, 37, 96);rgb(91, 32, 96);rgb(96, 32, 37);rgb(96, 91, 32);rgb(37, 96, 32)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;5.24" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="5.24" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(31, 80, 27);rgb(27, 80, 76);rgb(27, 31, 80);rgb(76, 27, 80);rgb(80, 27, 31);rgb(80, 76, 27);rgb(31, 80, 27)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;5.24" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(740 610.1)"><animateTransform attributeName="transform" type="translate" values="740 616.06;740 610.1" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(45, 115, 38);rgb(38, 115, 108);rgb(38, 45, 115);rgb(108, 38, 115);rgb(115, 38, 45);rgb(115, 108, 38);rgb(45, 115, 38)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="7.76" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(37, 96, 32);rgb(32, 96, 91);rgb(32, 37, 96);rgb(91, 32, 96);rgb(96, 32, 37);rgb(96, 91, 32);rgb(37, 96, 32)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;7.76" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="7.76" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(31, 80, 27);rgb(27, 80, 76);rgb(27, 31, 80);rgb(76, 27, 80);rgb(80, 27, 31);rgb(80, 76, 27);rgb(31, 80, 27)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;7.76" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(720 618.87)"><animateTransform attributeName="transform" type="translate" values="720 627.61;720 618.87" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(45, 115, 38);rgb(38, 115, 108);rgb(38, 45, 115);rgb(108, 38, 115);rgb(115, 38, 45);rgb(115, 108, 38);rgb(45, 115, 38)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="10.17" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(37, 96, 32);rgb(32, 96, 91);rgb(32, 37, 96);rgb(91, 32, 96);rgb(96, 32, 37);rgb(96, 91, 32);rgb(37, 96, 32)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;10.17" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="10.17" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(31, 80, 27);rgb(27, 80, 76);rgb(27, 31, 80);rgb(76, 27, 80);rgb(80, 27, 31);rgb(80, 76, 27);rgb(31, 80, 27)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;10.17" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(860 569.87)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(36, 77, 25);rgb(25, 77, 66);rgb(25, 36, 77);rgb(66, 25, 77);rgb(77, 25, 36);rgb(77, 66, 25);rgb(36, 77, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(30, 64, 21);rgb(21, 64, 55);rgb(21, 30, 64);rgb(55, 21, 64);rgb(64, 21, 30);rgb(64, 55, 21);rgb(30, 64, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 54, 18);rgb(18, 54, 46);rgb(18, 25, 54);rgb(46, 18, 54);rgb(54, 18, 25);rgb(54, 46, 18);rgb(25, 54, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(840 565.01)"><animateTransform attributeName="transform" type="translate" values="840 581.42;840 565.01" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 115, 38);rgb(38, 115, 99);rgb(38, 54, 115);rgb(99, 38, 115);rgb(115, 38, 54);rgb(115, 99, 38);rgb(54, 115, 38)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="16.81" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(45, 96, 32);rgb(32, 96, 83);rgb(32, 45, 96);rgb(83, 32, 96);rgb(96, 32, 45);rgb(96, 83, 32);rgb(45, 96, 32)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;16.81" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="16.81" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(37, 80, 27);rgb(27, 80, 70);rgb(27, 37, 80);rgb(70, 27, 80);rgb(80, 27, 37);rgb(80, 70, 27);rgb(37, 80, 27)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;16.81" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(820 565.56)"><animateTransform attributeName="transform" type="translate" values="820 592.97;820 565.56" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(62, 134, 45);rgb(45, 134, 116);rgb(45, 62, 134);rgb(116, 45, 134);rgb(134, 45, 62);rgb(134, 116, 45);rgb(62, 134, 45)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="26.33" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(52, 112, 37);rgb(37, 112, 97);rgb(37, 52, 112);rgb(97, 37, 112);rgb(112, 37, 52);rgb(112, 97, 37);rgb(52, 112, 37)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;26.33" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="26.33" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(44, 94, 31);rgb(31, 94, 81);rgb(31, 44, 94);rgb(81, 31, 94);rgb(94, 31, 44);rgb(94, 81, 31);rgb(44, 94, 31)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;26.33" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(800 579.16)"><animateTransform attributeName="transform" type="translate" values="800 604.51;800 579.16" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(62, 134, 45);rgb(45, 134, 116);rgb(45, 62, 134);rgb(116, 45, 134);rgb(134, 45, 62);rgb(134, 116, 45);rgb(62, 134, 45)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="24.56" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(52, 112, 37);rgb(37, 112, 97);rgb(37, 52, 112);rgb(97, 37, 112);rgb(112, 37, 52);rgb(112, 97, 37);rgb(52, 112, 37)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;24.56" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="24.56" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(44, 94, 31);rgb(31, 94, 81);rgb(31, 44, 94);rgb(81, 31, 94);rgb(94, 31, 44);rgb(94, 81, 31);rgb(44, 94, 31)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;24.56" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(780 599.65)"><animateTransform attributeName="transform" type="translate" values="780 616.06;780 599.65" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 115, 38);rgb(38, 115, 99);rgb(38, 54, 115);rgb(99, 38, 115);rgb(115, 38, 54);rgb(115, 99, 38);rgb(54, 115, 38)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="16.81" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(45, 96, 32);rgb(32, 96, 83);rgb(32, 45, 96);rgb(83, 32, 96);rgb(96, 32, 45);rgb(96, 83, 32);rgb(45, 96, 32)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;16.81" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="16.81" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(37, 80, 27);rgb(27, 80, 70);rgb(27, 37, 80);rgb(70, 27, 80);rgb(80, 27, 37);rgb(80, 70, 27);rgb(37, 80, 27)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;16.81" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(760 616.2)"><animateTransform attributeName="transform" type="translate" values="760 627.61;760 616.2" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 115, 38);rgb(38, 115, 99);rgb(38, 54, 115);rgb(99, 38, 115);rgb(115, 38, 54);rgb(115, 99, 38);rgb(54, 115, 38)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="12.47" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(45, 96, 32);rgb(32, 96, 83);rgb(32, 45, 96);rgb(83, 32, 96);rgb(96, 32, 45);rgb(96, 83, 32);rgb(45, 96, 32)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;12.47" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="12.47" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(37, 80, 27);rgb(27, 80, 70);rgb(27, 37, 80);rgb(70, 27, 80);rgb(80, 27, 37);rgb(80, 70, 27);rgb(37, 80, 27)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;12.47" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(740 639.15)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(36, 77, 25);rgb(25, 77, 66);rgb(25, 36, 77);rgb(66, 25, 77);rgb(77, 25, 36);rgb(77, 66, 25);rgb(36, 77, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(30, 64, 21);rgb(21, 64, 55);rgb(21, 30, 64);rgb(55, 21, 64);rgb(64, 21, 30);rgb(64, 55, 21);rgb(30, 64, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(25, 54, 18);rgb(18, 54, 46);rgb(18, 25, 54);rgb(46, 18, 54);rgb(54, 18, 25);rgb(54, 46, 18);rgb(25, 54, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(880 570.02)"><animateTransform attributeName="transform" type="translate" values="880 581.42;880 570.02" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(62, 115, 38);rgb(38, 115, 91);rgb(38, 62, 115);rgb(91, 38, 115);rgb(115, 38, 62);rgb(115, 91, 38);rgb(62, 115, 38)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="12.47" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(52, 96, 32);rgb(32, 96, 76);rgb(32, 52, 96);rgb(76, 32, 96);rgb(96, 32, 52);rgb(96, 76, 32);rgb(52, 96, 32)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;12.47" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="12.47" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(44, 80, 27);rgb(27, 80, 63);rgb(27, 44, 80);rgb(63, 27, 80);rgb(80, 27, 44);rgb(80, 63, 27);rgb(44, 80, 27)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;12.47" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(860 565.56)"><animateTransform attributeName="transform" type="translate" values="860 592.97;860 565.56" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(73, 134, 45);rgb(45, 134, 106);rgb(45, 73, 134);rgb(106, 45, 134);rgb(134, 45, 73);rgb(134, 106, 45);rgb(73, 134, 45)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="26.33" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(61, 112, 37);rgb(37, 112, 88);rgb(37, 61, 112);rgb(88, 37, 112);rgb(112, 37, 61);rgb(112, 88, 37);rgb(61, 112, 37)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;26.33" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="26.33" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(51, 94, 31);rgb(31, 94, 74);rgb(31, 51, 94);rgb(74, 31, 94);rgb(94, 31, 51);rgb(94, 74, 31);rgb(51, 94, 31)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;26.33" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(840 590.56)"><animateTransform attributeName="transform" type="translate" values="840 604.51;840 590.56" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(62, 115, 38);rgb(38, 115, 91);rgb(38, 62, 115);rgb(91, 38, 115);rgb(115, 38, 62);rgb(115, 91, 38);rgb(62, 115, 38)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="14.68" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(52, 96, 32);rgb(32, 96, 76);rgb(32, 52, 96);rgb(76, 32, 96);rgb(96, 32, 52);rgb(96, 76, 32);rgb(52, 96, 32)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;14.68" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="14.68" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(44, 80, 27);rgb(27, 80, 63);rgb(27, 44, 80);rgb(63, 27, 80);rgb(80, 27, 44);rgb(80, 63, 27);rgb(44, 80, 27)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;14.68" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(820 604.66)"><animateTransform attributeName="transform" type="translate" values="820 616.06;820 604.66" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(62, 115, 38);rgb(38, 115, 91);rgb(38, 62, 115);rgb(91, 38, 115);rgb(115, 38, 62);rgb(115, 91, 38);rgb(62, 115, 38)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="12.47" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(52, 96, 32);rgb(32, 96, 76);rgb(32, 52, 96);rgb(76, 32, 96);rgb(96, 32, 52);rgb(96, 76, 32);rgb(52, 96, 32)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;12.47" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="12.47" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(44, 80, 27);rgb(27, 80, 63);rgb(27, 44, 80);rgb(63, 27, 80);rgb(80, 27, 44);rgb(80, 63, 27);rgb(44, 80, 27)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;12.47" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(800 611.2)"><animateTransform attributeName="transform" type="translate" values="800 627.61;800 611.2" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(62, 115, 38);rgb(38, 115, 91);rgb(38, 62, 115);rgb(91, 38, 115);rgb(115, 38, 62);rgb(115, 91, 38);rgb(62, 115, 38)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="16.81" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(52, 96, 32);rgb(32, 96, 76);rgb(32, 52, 96);rgb(76, 32, 96);rgb(96, 32, 52);rgb(96, 76, 32);rgb(52, 96, 32)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;16.81" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="16.81" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(44, 80, 27);rgb(27, 80, 63);rgb(27, 44, 80);rgb(63, 27, 80);rgb(80, 27, 44);rgb(80, 63, 27);rgb(44, 80, 27)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;16.81" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(780 592.75)"><animateTransform attributeName="transform" type="translate" values="780 639.15;780 592.75" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(104, 191, 64);rgb(64, 191, 151);rgb(64, 104, 191);rgb(151, 64, 191);rgb(191, 64, 104);rgb(191, 151, 64);rgb(104, 191, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="42.78" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(87, 160, 53);rgb(53, 160, 126);rgb(53, 87, 160);rgb(126, 53, 160);rgb(160, 53, 87);rgb(160, 126, 53);rgb(87, 160, 53)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;42.78" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="42.78" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(73, 134, 45);rgb(45, 134, 106);rgb(45, 73, 134);rgb(106, 45, 134);rgb(134, 45, 73);rgb(134, 106, 45);rgb(73, 134, 45)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;42.78" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(760 641.96)"><animateTransform attributeName="transform" type="translate" values="760 650.7;760 641.96" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(62, 115, 38);rgb(38, 115, 91);rgb(38, 62, 115);rgb(91, 38, 115);rgb(115, 38, 62);rgb(115, 91, 38);rgb(62, 115, 38)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="10.17" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(52, 96, 32);rgb(32, 96, 76);rgb(32, 52, 96);rgb(76, 32, 96);rgb(96, 32, 52);rgb(96, 76, 32);rgb(52, 96, 32)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;10.17" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="10.17" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(44, 80, 27);rgb(27, 80, 63);rgb(27, 44, 80);rgb(63, 27, 80);rgb(80, 27, 44);rgb(80, 63, 27);rgb(44, 80, 27)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;10.17" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(900 559.78)"><animateTransform attributeName="transform" type="translate" values="900 592.97;900 559.78" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(95, 153, 51);rgb(51, 153, 109);rgb(51, 95, 153);rgb(109, 51, 153);rgb(153, 51, 95);rgb(153, 109, 51);rgb(95, 153, 51)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="31.34" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(80, 128, 43);rgb(43, 128, 91);rgb(43, 80, 128);rgb(91, 43, 128);rgb(128, 43, 80);rgb(128, 91, 43);rgb(80, 128, 43)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;31.34" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="31.34" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(67, 107, 36);rgb(36, 107, 76);rgb(36, 67, 107);rgb(76, 36, 107);rgb(107, 36, 67);rgb(107, 76, 36);rgb(67, 107, 36)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;31.34" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(880 595.77)"><animateTransform attributeName="transform" type="translate" values="880 604.51;880 595.77" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(71, 115, 38);rgb(38, 115, 82);rgb(38, 71, 115);rgb(82, 38, 115);rgb(115, 38, 71);rgb(115, 82, 38);rgb(71, 115, 38)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="10.17" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(60, 96, 32);rgb(32, 96, 68);rgb(32, 60, 96);rgb(68, 32, 96);rgb(96, 32, 60);rgb(96, 68, 32);rgb(60, 96, 32)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;10.17" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="10.17" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(50, 80, 27);rgb(27, 80, 57);rgb(27, 50, 80);rgb(57, 27, 80);rgb(80, 27, 50);rgb(80, 57, 27);rgb(50, 80, 27)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;10.17" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(860 599.65)"><animateTransform attributeName="transform" type="translate" values="860 616.06;860 599.65" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(71, 115, 38);rgb(38, 115, 82);rgb(38, 71, 115);rgb(82, 38, 115);rgb(115, 38, 71);rgb(115, 82, 38);rgb(71, 115, 38)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="16.81" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(60, 96, 32);rgb(32, 96, 68);rgb(32, 60, 96);rgb(68, 32, 96);rgb(96, 32, 60);rgb(96, 68, 32);rgb(60, 96, 32)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;16.81" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="16.81" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(50, 80, 27);rgb(27, 80, 57);rgb(27, 50, 80);rgb(57, 27, 80);rgb(80, 27, 50);rgb(80, 57, 27);rgb(50, 80, 27)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;16.81" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(840 627.61)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(48, 77, 25);rgb(25, 77, 54);rgb(25, 48, 77);rgb(54, 25, 77);rgb(77, 25, 48);rgb(77, 54, 25);rgb(48, 77, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(40, 64, 21);rgb(21, 64, 46);rgb(21, 40, 64);rgb(46, 21, 64);rgb(64, 21, 40);rgb(64, 46, 21);rgb(40, 64, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(33, 54, 18);rgb(18, 54, 38);rgb(18, 33, 54);rgb(38, 18, 54);rgb(54, 18, 33);rgb(54, 38, 18);rgb(33, 54, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(820 639.15)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(48, 77, 25);rgb(25, 77, 54);rgb(25, 48, 77);rgb(54, 25, 77);rgb(77, 25, 48);rgb(77, 54, 25);rgb(48, 77, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(40, 64, 21);rgb(21, 64, 46);rgb(21, 40, 64);rgb(46, 21, 64);rgb(64, 21, 40);rgb(64, 46, 21);rgb(40, 64, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(33, 54, 18);rgb(18, 54, 38);rgb(18, 33, 54);rgb(38, 18, 54);rgb(54, 18, 33);rgb(54, 38, 18);rgb(33, 54, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(800 650.7)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(48, 77, 25);rgb(25, 77, 54);rgb(25, 48, 77);rgb(54, 25, 77);rgb(77, 25, 48);rgb(77, 54, 25);rgb(48, 77, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(40, 64, 21);rgb(21, 64, 46);rgb(21, 40, 64);rgb(46, 21, 64);rgb(64, 21, 40);rgb(64, 46, 21);rgb(40, 64, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(33, 54, 18);rgb(18, 54, 38);rgb(18, 33, 54);rgb(38, 18, 54);rgb(54, 18, 33);rgb(54, 38, 18);rgb(33, 54, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(780 662.25)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(48, 77, 25);rgb(25, 77, 54);rgb(25, 48, 77);rgb(54, 25, 77);rgb(77, 25, 48);rgb(77, 54, 25);rgb(48, 77, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(40, 64, 21);rgb(21, 64, 46);rgb(21, 40, 64);rgb(46, 21, 64);rgb(64, 21, 40);rgb(64, 46, 21);rgb(40, 64, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(33, 54, 18);rgb(18, 54, 38);rgb(18, 33, 54);rgb(38, 18, 54);rgb(54, 18, 33);rgb(54, 38, 18);rgb(33, 54, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(920 604.51)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 77, 25);rgb(25, 77, 48);rgb(25, 54, 77);rgb(48, 25, 77);rgb(77, 25, 54);rgb(77, 48, 25);rgb(54, 77, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(45, 64, 21);rgb(21, 64, 41);rgb(21, 45, 64);rgb(41, 21, 64);rgb(64, 21, 45);rgb(64, 41, 21);rgb(45, 64, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(37, 54, 18);rgb(18, 54, 34);rgb(18, 37, 54);rgb(34, 18, 54);rgb(54, 18, 37);rgb(54, 34, 18);rgb(37, 54, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(900 616.06)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 77, 25);rgb(25, 77, 48);rgb(25, 54, 77);rgb(48, 25, 77);rgb(77, 25, 54);rgb(77, 48, 25);rgb(54, 77, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(45, 64, 21);rgb(21, 64, 41);rgb(21, 45, 64);rgb(41, 21, 64);rgb(64, 21, 45);rgb(64, 41, 21);rgb(45, 64, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(37, 54, 18);rgb(18, 54, 34);rgb(18, 37, 54);rgb(34, 18, 54);rgb(54, 18, 37);rgb(54, 34, 18);rgb(37, 54, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(880 627.61)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 77, 25);rgb(25, 77, 48);rgb(25, 54, 77);rgb(48, 25, 77);rgb(77, 25, 54);rgb(77, 48, 25);rgb(54, 77, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(45, 64, 21);rgb(21, 64, 41);rgb(21, 45, 64);rgb(41, 21, 64);rgb(64, 21, 45);rgb(64, 41, 21);rgb(45, 64, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(37, 54, 18);rgb(18, 54, 34);rgb(18, 37, 54);rgb(34, 18, 54);rgb(54, 18, 37);rgb(54, 34, 18);rgb(37, 54, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(860 639.15)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 77, 25);rgb(25, 77, 48);rgb(25, 54, 77);rgb(48, 25, 77);rgb(77, 25, 54);rgb(77, 48, 25);rgb(54, 77, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(45, 64, 21);rgb(21, 64, 41);rgb(21, 45, 64);rgb(41, 21, 64);rgb(64, 21, 45);rgb(64, 41, 21);rgb(45, 64, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(37, 54, 18);rgb(18, 54, 34);rgb(18, 37, 54);rgb(34, 18, 54);rgb(54, 18, 37);rgb(54, 34, 18);rgb(37, 54, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(840 650.7)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 77, 25);rgb(25, 77, 48);rgb(25, 54, 77);rgb(48, 25, 77);rgb(77, 25, 54);rgb(77, 48, 25);rgb(54, 77, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(45, 64, 21);rgb(21, 64, 41);rgb(21, 45, 64);rgb(41, 21, 64);rgb(64, 21, 45);rgb(64, 41, 21);rgb(45, 64, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(37, 54, 18);rgb(18, 54, 34);rgb(18, 37, 54);rgb(34, 18, 54);rgb(54, 18, 37);rgb(54, 34, 18);rgb(37, 54, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(820 662.25)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 77, 25);rgb(25, 77, 48);rgb(25, 54, 77);rgb(48, 25, 77);rgb(77, 25, 54);rgb(77, 48, 25);rgb(54, 77, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(45, 64, 21);rgb(21, 64, 41);rgb(21, 45, 64);rgb(41, 21, 64);rgb(64, 21, 45);rgb(64, 41, 21);rgb(45, 64, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(37, 54, 18);rgb(18, 54, 34);rgb(18, 37, 54);rgb(34, 18, 54);rgb(54, 18, 37);rgb(54, 34, 18);rgb(37, 54, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(800 673.79)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 77, 25);rgb(25, 77, 48);rgb(25, 54, 77);rgb(48, 25, 77);rgb(77, 25, 54);rgb(77, 48, 25);rgb(54, 77, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(45, 64, 21);rgb(21, 64, 41);rgb(21, 45, 64);rgb(41, 21, 64);rgb(64, 21, 45);rgb(64, 41, 21);rgb(45, 64, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(37, 54, 18);rgb(18, 54, 34);rgb(18, 37, 54);rgb(34, 18, 54);rgb(54, 18, 37);rgb(54, 34, 18);rgb(37, 54, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(940 616.06)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(60, 77, 25);rgb(25, 77, 43);rgb(25, 60, 77);rgb(43, 25, 77);rgb(77, 25, 60);rgb(77, 43, 25);rgb(60, 77, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(50, 64, 21);rgb(21, 64, 36);rgb(21, 50, 64);rgb(36, 21, 64);rgb(64, 21, 50);rgb(64, 36, 21);rgb(50, 64, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(42, 54, 18);rgb(18, 54, 30);rgb(18, 42, 54);rgb(30, 18, 54);rgb(54, 18, 42);rgb(54, 30, 18);rgb(42, 54, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(920 627.61)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(60, 77, 25);rgb(25, 77, 43);rgb(25, 60, 77);rgb(43, 25, 77);rgb(77, 25, 60);rgb(77, 43, 25);rgb(60, 77, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(50, 64, 21);rgb(21, 64, 36);rgb(21, 50, 64);rgb(36, 21, 64);rgb(64, 21, 50);rgb(64, 36, 21);rgb(50, 64, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(42, 54, 18);rgb(18, 54, 30);rgb(18, 42, 54);rgb(30, 18, 54);rgb(54, 18, 42);rgb(54, 30, 18);rgb(42, 54, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(900 630.41)"><animateTransform attributeName="transform" type="translate" values="900 639.15;900 630.41" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(89, 115, 38);rgb(38, 115, 64);rgb(38, 89, 115);rgb(64, 38, 115);rgb(115, 38, 89);rgb(115, 64, 38);rgb(89, 115, 38)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="10.17" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(75, 96, 32);rgb(32, 96, 53);rgb(32, 75, 96);rgb(53, 32, 96);rgb(96, 32, 75);rgb(96, 53, 32);rgb(75, 96, 32)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;10.17" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="10.17" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(62, 80, 27);rgb(27, 80, 45);rgb(27, 62, 80);rgb(45, 27, 80);rgb(80, 27, 62);rgb(80, 45, 27);rgb(62, 80, 27)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;10.17" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(880 647.65)"><animateTransform attributeName="transform" type="translate" values="880 650.7;880 647.65" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(89, 115, 38);rgb(38, 115, 64);rgb(38, 89, 115);rgb(64, 38, 115);rgb(115, 38, 89);rgb(115, 64, 38);rgb(89, 115, 38)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="5.24" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(75, 96, 32);rgb(32, 96, 53);rgb(32, 75, 96);rgb(53, 32, 96);rgb(96, 32, 75);rgb(96, 53, 32);rgb(75, 96, 32)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;5.24" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="5.24" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(62, 80, 27);rgb(27, 80, 45);rgb(27, 62, 80);rgb(45, 27, 80);rgb(80, 27, 62);rgb(80, 45, 27);rgb(62, 80, 27)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;5.24" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(860 662.25)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(60, 77, 25);rgb(25, 77, 43);rgb(25, 60, 77);rgb(43, 25, 77);rgb(77, 25, 60);rgb(77, 43, 25);rgb(60, 77, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(50, 64, 21);rgb(21, 64, 36);rgb(21, 50, 64);rgb(36, 21, 64);rgb(64, 21, 50);rgb(64, 36, 21);rgb(50, 64, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(42, 54, 18);rgb(18, 54, 30);rgb(18, 42, 54);rgb(30, 18, 54);rgb(54, 18, 42);rgb(54, 30, 18);rgb(42, 54, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(840 673.79)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(60, 77, 25);rgb(25, 77, 43);rgb(25, 60, 77);rgb(43, 25, 77);rgb(77, 25, 60);rgb(77, 43, 25);rgb(60, 77, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(50, 64, 21);rgb(21, 64, 36);rgb(21, 50, 64);rgb(36, 21, 64);rgb(64, 21, 50);rgb(64, 36, 21);rgb(50, 64, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(42, 54, 18);rgb(18, 54, 30);rgb(18, 42, 54);rgb(30, 18, 54);rgb(54, 18, 42);rgb(54, 30, 18);rgb(42, 54, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(820 685.34)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(60, 77, 25);rgb(25, 77, 43);rgb(25, 60, 77);rgb(43, 25, 77);rgb(77, 25, 60);rgb(77, 43, 25);rgb(60, 77, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(50, 64, 21);rgb(21, 64, 36);rgb(21, 50, 64);rgb(36, 21, 64);rgb(64, 21, 50);rgb(64, 36, 21);rgb(50, 64, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(42, 54, 18);rgb(18, 54, 30);rgb(18, 42, 54);rgb(30, 18, 54);rgb(54, 18, 42);rgb(54, 30, 18);rgb(42, 54, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(960 627.61)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(65, 77, 25);rgb(25, 77, 37);rgb(25, 65, 77);rgb(37, 25, 77);rgb(77, 25, 65);rgb(77, 37, 25);rgb(65, 77, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(55, 64, 21);rgb(21, 64, 31);rgb(21, 55, 64);rgb(31, 21, 64);rgb(64, 21, 55);rgb(64, 31, 21);rgb(55, 64, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(46, 54, 18);rgb(18, 54, 26);rgb(18, 46, 54);rgb(26, 18, 54);rgb(54, 18, 46);rgb(54, 26, 18);rgb(46, 54, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(940 639.15)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(65, 77, 25);rgb(25, 77, 37);rgb(25, 65, 77);rgb(37, 25, 77);rgb(77, 25, 65);rgb(77, 37, 25);rgb(65, 77, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(55, 64, 21);rgb(21, 64, 31);rgb(21, 55, 64);rgb(31, 21, 64);rgb(64, 21, 55);rgb(64, 31, 21);rgb(55, 64, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(46, 54, 18);rgb(18, 54, 26);rgb(18, 46, 54);rgb(26, 18, 54);rgb(54, 18, 46);rgb(54, 26, 18);rgb(46, 54, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(920 650.7)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(65, 77, 25);rgb(25, 77, 37);rgb(25, 65, 77);rgb(37, 25, 77);rgb(77, 25, 65);rgb(77, 37, 25);rgb(65, 77, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(55, 64, 21);rgb(21, 64, 31);rgb(21, 55, 64);rgb(31, 21, 64);rgb(64, 21, 55);rgb(64, 31, 21);rgb(55, 64, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(46, 54, 18);rgb(18, 54, 26);rgb(18, 46, 54);rgb(26, 18, 54);rgb(54, 18, 46);rgb(54, 26, 18);rgb(46, 54, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(900 662.25)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(65, 77, 25);rgb(25, 77, 37);rgb(25, 65, 77);rgb(37, 25, 77);rgb(77, 25, 65);rgb(77, 37, 25);rgb(65, 77, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(55, 64, 21);rgb(21, 64, 31);rgb(21, 55, 64);rgb(31, 21, 64);rgb(64, 21, 55);rgb(64, 31, 21);rgb(55, 64, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(46, 54, 18);rgb(18, 54, 26);rgb(18, 46, 54);rgb(26, 18, 54);rgb(54, 18, 46);rgb(54, 26, 18);rgb(46, 54, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(880 673.79)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(65, 77, 25);rgb(25, 77, 37);rgb(25, 65, 77);rgb(37, 25, 77);rgb(77, 25, 65);rgb(77, 37, 25);rgb(65, 77, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(55, 64, 21);rgb(21, 64, 31);rgb(21, 55, 64);rgb(31, 21, 64);rgb(64, 21, 55);rgb(64, 31, 21);rgb(55, 64, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(46, 54, 18);rgb(18, 54, 26);rgb(18, 46, 54);rgb(26, 18, 54);rgb(54, 18, 46);rgb(54, 26, 18);rgb(46, 54, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(860 685.34)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(65, 77, 25);rgb(25, 77, 37);rgb(25, 65, 77);rgb(37, 25, 77);rgb(77, 25, 65);rgb(77, 37, 25);rgb(65, 77, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(55, 64, 21);rgb(21, 64, 31);rgb(21, 55, 64);rgb(31, 21, 64);rgb(64, 21, 55);rgb(64, 31, 21);rgb(55, 64, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(46, 54, 18);rgb(18, 54, 26);rgb(18, 46, 54);rgb(26, 18, 54);rgb(54, 18, 46);rgb(54, 26, 18);rgb(46, 54, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(840 696.89)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(65, 77, 25);rgb(25, 77, 37);rgb(25, 65, 77);rgb(37, 25, 77);rgb(77, 25, 65);rgb(77, 37, 25);rgb(65, 77, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(55, 64, 21);rgb(21, 64, 31);rgb(21, 55, 64);rgb(31, 21, 64);rgb(64, 21, 55);rgb(64, 31, 21);rgb(55, 64, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(46, 54, 18);rgb(18, 54, 26);rgb(18, 46, 54);rgb(26, 18, 54);rgb(54, 18, 46);rgb(54, 26, 18);rgb(46, 54, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(980 639.15)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(71, 77, 25);rgb(25, 77, 31);rgb(25, 71, 77);rgb(31, 25, 77);rgb(77, 25, 71);rgb(77, 31, 25);rgb(71, 77, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(60, 64, 21);rgb(21, 64, 26);rgb(21, 60, 64);rgb(26, 21, 64);rgb(64, 21, 60);rgb(64, 26, 21);rgb(60, 64, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(50, 54, 18);rgb(18, 54, 21);rgb(18, 50, 54);rgb(21, 18, 54);rgb(54, 18, 50);rgb(54, 21, 18);rgb(50, 54, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(960 650.7)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(71, 77, 25);rgb(25, 77, 31);rgb(25, 71, 77);rgb(31, 25, 77);rgb(77, 25, 71);rgb(77, 31, 25);rgb(71, 77, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(60, 64, 21);rgb(21, 64, 26);rgb(21, 60, 64);rgb(26, 21, 64);rgb(64, 21, 60);rgb(64, 26, 21);rgb(60, 64, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(50, 54, 18);rgb(18, 54, 21);rgb(18, 50, 54);rgb(21, 18, 54);rgb(54, 18, 50);rgb(54, 21, 18);rgb(50, 54, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(940 662.25)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(71, 77, 25);rgb(25, 77, 31);rgb(25, 71, 77);rgb(31, 25, 77);rgb(77, 25, 71);rgb(77, 31, 25);rgb(71, 77, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(60, 64, 21);rgb(21, 64, 26);rgb(21, 60, 64);rgb(26, 21, 64);rgb(64, 21, 60);rgb(64, 26, 21);rgb(60, 64, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(50, 54, 18);rgb(18, 54, 21);rgb(18, 50, 54);rgb(21, 18, 54);rgb(54, 18, 50);rgb(54, 21, 18);rgb(50, 54, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(920 673.79)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(71, 77, 25);rgb(25, 77, 31);rgb(25, 71, 77);rgb(31, 25, 77);rgb(77, 25, 71);rgb(77, 31, 25);rgb(71, 77, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(60, 64, 21);rgb(21, 64, 26);rgb(21, 60, 64);rgb(26, 21, 64);rgb(64, 21, 60);rgb(64, 26, 21);rgb(60, 64, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(50, 54, 18);rgb(18, 54, 21);rgb(18, 50, 54);rgb(21, 18, 54);rgb(54, 18, 50);rgb(54, 21, 18);rgb(50, 54, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(900 685.34)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(71, 77, 25);rgb(25, 77, 31);rgb(25, 71, 77);rgb(31, 25, 77);rgb(77, 25, 71);rgb(77, 31, 25);rgb(71, 77, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(60, 64, 21);rgb(21, 64, 26);rgb(21, 60, 64);rgb(26, 21, 64);rgb(64, 21, 60);rgb(64, 26, 21);rgb(60, 64, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(50, 54, 18);rgb(18, 54, 21);rgb(18, 50, 54);rgb(21, 18, 54);rgb(54, 18, 50);rgb(54, 21, 18);rgb(50, 54, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(880 696.89)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(71, 77, 25);rgb(25, 77, 31);rgb(25, 71, 77);rgb(31, 25, 77);rgb(77, 25, 71);rgb(77, 31, 25);rgb(71, 77, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(60, 64, 21);rgb(21, 64, 26);rgb(21, 60, 64);rgb(26, 21, 64);rgb(64, 21, 60);rgb(64, 26, 21);rgb(60, 64, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(50, 54, 18);rgb(18, 54, 21);rgb(18, 50, 54);rgb(21, 18, 54);rgb(54, 18, 50);rgb(54, 21, 18);rgb(50, 54, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(860 708.44)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(71, 77, 25);rgb(25, 77, 31);rgb(25, 71, 77);rgb(31, 25, 77);rgb(77, 25, 71);rgb(77, 31, 25);rgb(71, 77, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(60, 64, 21);rgb(21, 64, 26);rgb(21, 60, 64);rgb(26, 21, 64);rgb(64, 21, 60);rgb(64, 26, 21);rgb(60, 64, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(50, 54, 18);rgb(18, 54, 21);rgb(18, 50, 54);rgb(21, 18, 54);rgb(54, 18, 50);rgb(54, 21, 18);rgb(50, 54, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(1000 650.7)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 76, 25);rgb(26, 77, 25);rgb(25, 77, 76);rgb(25, 26, 77);rgb(76, 25, 77);rgb(77, 25, 26);rgb(77, 76, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 63, 21);rgb(22, 64, 21);rgb(21, 64, 63);rgb(21, 22, 64);rgb(63, 21, 64);rgb(64, 21, 22);rgb(64, 63, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 53, 18);rgb(18, 54, 18);rgb(18, 54, 53);rgb(18, 18, 54);rgb(53, 18, 54);rgb(54, 18, 18);rgb(54, 53, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(980 662.25)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 76, 25);rgb(26, 77, 25);rgb(25, 77, 76);rgb(25, 26, 77);rgb(76, 25, 77);rgb(77, 25, 26);rgb(77, 76, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 63, 21);rgb(22, 64, 21);rgb(21, 64, 63);rgb(21, 22, 64);rgb(63, 21, 64);rgb(64, 21, 22);rgb(64, 63, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 53, 18);rgb(18, 54, 18);rgb(18, 54, 53);rgb(18, 18, 54);rgb(53, 18, 54);rgb(54, 18, 18);rgb(54, 53, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(960 673.79)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 76, 25);rgb(26, 77, 25);rgb(25, 77, 76);rgb(25, 26, 77);rgb(76, 25, 77);rgb(77, 25, 26);rgb(77, 76, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 63, 21);rgb(22, 64, 21);rgb(21, 64, 63);rgb(21, 22, 64);rgb(63, 21, 64);rgb(64, 21, 22);rgb(64, 63, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 53, 18);rgb(18, 54, 18);rgb(18, 54, 53);rgb(18, 18, 54);rgb(53, 18, 54);rgb(54, 18, 18);rgb(54, 53, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(940 685.34)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 76, 25);rgb(26, 77, 25);rgb(25, 77, 76);rgb(25, 26, 77);rgb(76, 25, 77);rgb(77, 25, 26);rgb(77, 76, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 63, 21);rgb(22, 64, 21);rgb(21, 64, 63);rgb(21, 22, 64);rgb(63, 21, 64);rgb(64, 21, 22);rgb(64, 63, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 53, 18);rgb(18, 54, 18);rgb(18, 54, 53);rgb(18, 18, 54);rgb(53, 18, 54);rgb(54, 18, 18);rgb(54, 53, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(920 696.89)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 76, 25);rgb(26, 77, 25);rgb(25, 77, 76);rgb(25, 26, 77);rgb(76, 25, 77);rgb(77, 25, 26);rgb(77, 76, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 63, 21);rgb(22, 64, 21);rgb(21, 64, 63);rgb(21, 22, 64);rgb(63, 21, 64);rgb(64, 21, 22);rgb(64, 63, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 53, 18);rgb(18, 54, 18);rgb(18, 54, 53);rgb(18, 18, 54);rgb(53, 18, 54);rgb(54, 18, 18);rgb(54, 53, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(900 708.44)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 76, 25);rgb(26, 77, 25);rgb(25, 77, 76);rgb(25, 26, 77);rgb(76, 25, 77);rgb(77, 25, 26);rgb(77, 76, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 63, 21);rgb(22, 64, 21);rgb(21, 64, 63);rgb(21, 22, 64);rgb(63, 21, 64);rgb(64, 21, 22);rgb(64, 63, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 53, 18);rgb(18, 54, 18);rgb(18, 54, 53);rgb(18, 18, 54);rgb(53, 18, 54);rgb(54, 18, 18);rgb(54, 53, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(880 719.98)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 76, 25);rgb(26, 77, 25);rgb(25, 77, 76);rgb(25, 26, 77);rgb(76, 25, 77);rgb(77, 25, 26);rgb(77, 76, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 63, 21);rgb(22, 64, 21);rgb(21, 64, 63);rgb(21, 22, 64);rgb(63, 21, 64);rgb(64, 21, 22);rgb(64, 63, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 53, 18);rgb(18, 54, 18);rgb(18, 54, 53);rgb(18, 18, 54);rgb(53, 18, 54);rgb(54, 18, 18);rgb(54, 53, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(1020 662.25)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 70, 25);rgb(32, 77, 25);rgb(25, 77, 70);rgb(25, 32, 77);rgb(70, 25, 77);rgb(77, 25, 32);rgb(77, 70, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 58, 21);rgb(27, 64, 21);rgb(21, 64, 58);rgb(21, 27, 64);rgb(58, 21, 64);rgb(64, 21, 27);rgb(64, 58, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 49, 18);rgb(23, 54, 18);rgb(18, 54, 49);rgb(18, 23, 54);rgb(49, 18, 54);rgb(54, 18, 23);rgb(54, 49, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(1000 673.79)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 70, 25);rgb(32, 77, 25);rgb(25, 77, 70);rgb(25, 32, 77);rgb(70, 25, 77);rgb(77, 25, 32);rgb(77, 70, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 58, 21);rgb(27, 64, 21);rgb(21, 64, 58);rgb(21, 27, 64);rgb(58, 21, 64);rgb(64, 21, 27);rgb(64, 58, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 49, 18);rgb(23, 54, 18);rgb(18, 54, 49);rgb(18, 23, 54);rgb(49, 18, 54);rgb(54, 18, 23);rgb(54, 49, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(980 685.34)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 70, 25);rgb(32, 77, 25);rgb(25, 77, 70);rgb(25, 32, 77);rgb(70, 25, 77);rgb(77, 25, 32);rgb(77, 70, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 58, 21);rgb(27, 64, 21);rgb(21, 64, 58);rgb(21, 27, 64);rgb(58, 21, 64);rgb(64, 21, 27);rgb(64, 58, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 49, 18);rgb(23, 54, 18);rgb(18, 54, 49);rgb(18, 23, 54);rgb(49, 18, 54);rgb(54, 18, 23);rgb(54, 49, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(960 696.89)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 70, 25);rgb(32, 77, 25);rgb(25, 77, 70);rgb(25, 32, 77);rgb(70, 25, 77);rgb(77, 25, 32);rgb(77, 70, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 58, 21);rgb(27, 64, 21);rgb(21, 64, 58);rgb(21, 27, 64);rgb(58, 21, 64);rgb(64, 21, 27);rgb(64, 58, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 49, 18);rgb(23, 54, 18);rgb(18, 54, 49);rgb(18, 23, 54);rgb(49, 18, 54);rgb(54, 18, 23);rgb(54, 49, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(940 681.03)"><animateTransform attributeName="transform" type="translate" values="940 708.44;940 681.03" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(134, 122, 45);rgb(57, 134, 45);rgb(45, 134, 122);rgb(45, 57, 134);rgb(122, 45, 134);rgb(134, 45, 57);rgb(134, 122, 45)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="26.33" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(112, 102, 37);rgb(47, 112, 37);rgb(37, 112, 102);rgb(37, 47, 112);rgb(102, 37, 112);rgb(112, 37, 47);rgb(112, 102, 37)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;26.33" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="26.33" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(94, 85, 31);rgb(40, 94, 31);rgb(31, 94, 85);rgb(31, 40, 94);rgb(85, 31, 94);rgb(94, 31, 40);rgb(94, 85, 31)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;26.33" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(920 716.93)"><animateTransform attributeName="transform" type="translate" values="920 719.98;920 716.93" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(115, 105, 38);rgb(48, 115, 38);rgb(38, 115, 105);rgb(38, 48, 115);rgb(105, 38, 115);rgb(115, 38, 48);rgb(115, 105, 38)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="5.24" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(96, 87, 32);rgb(41, 96, 32);rgb(32, 96, 87);rgb(32, 41, 96);rgb(87, 32, 96);rgb(96, 32, 41);rgb(96, 87, 32)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;5.24" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="5.24" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(80, 73, 27);rgb(34, 80, 27);rgb(27, 80, 73);rgb(27, 34, 80);rgb(73, 27, 80);rgb(80, 27, 34);rgb(80, 73, 27)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;5.24" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(900 693.06)"><animateTransform attributeName="transform" type="translate" values="900 731.53;900 693.06" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(153, 139, 51);rgb(65, 153, 51);rgb(51, 153, 139);rgb(51, 65, 153);rgb(139, 51, 153);rgb(153, 51, 65);rgb(153, 139, 51)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="35.92" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(128, 117, 43);rgb(54, 128, 43);rgb(43, 128, 117);rgb(43, 54, 128);rgb(117, 43, 128);rgb(128, 43, 54);rgb(128, 117, 43)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;35.92" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="35.92" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(107, 98, 36);rgb(45, 107, 36);rgb(36, 107, 98);rgb(36, 45, 107);rgb(98, 36, 107);rgb(107, 36, 45);rgb(107, 98, 36)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;35.92" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(1040 646.39)"><animateTransform attributeName="transform" type="translate" values="1040 673.79;1040 646.39" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(134, 112, 45);rgb(67, 134, 45);rgb(45, 134, 112);rgb(45, 67, 134);rgb(112, 45, 134);rgb(134, 45, 67);rgb(134, 112, 45)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="26.33" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(112, 93, 37);rgb(56, 112, 37);rgb(37, 112, 93);rgb(37, 56, 112);rgb(93, 37, 112);rgb(112, 37, 56);rgb(112, 93, 37)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;26.33" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="26.33" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(94, 78, 31);rgb(47, 94, 31);rgb(31, 94, 78);rgb(31, 47, 94);rgb(78, 31, 94);rgb(94, 31, 47);rgb(94, 78, 31)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;26.33" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(1020 685.34)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 64, 25);rgb(38, 77, 25);rgb(25, 77, 64);rgb(25, 38, 77);rgb(64, 25, 77);rgb(77, 25, 38);rgb(77, 64, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 53, 21);rgb(32, 64, 21);rgb(21, 64, 53);rgb(21, 32, 64);rgb(53, 21, 64);rgb(64, 21, 32);rgb(64, 53, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 45, 18);rgb(27, 54, 18);rgb(18, 54, 45);rgb(18, 27, 54);rgb(45, 18, 54);rgb(54, 18, 27);rgb(54, 45, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(1000 696.89)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 64, 25);rgb(38, 77, 25);rgb(25, 77, 64);rgb(25, 38, 77);rgb(64, 25, 77);rgb(77, 25, 38);rgb(77, 64, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 53, 21);rgb(32, 64, 21);rgb(21, 64, 53);rgb(21, 32, 64);rgb(53, 21, 64);rgb(64, 21, 32);rgb(64, 53, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 45, 18);rgb(27, 54, 18);rgb(18, 54, 45);rgb(18, 27, 54);rgb(45, 18, 54);rgb(54, 18, 27);rgb(54, 45, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(980 708.44)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 64, 25);rgb(38, 77, 25);rgb(25, 77, 64);rgb(25, 38, 77);rgb(64, 25, 77);rgb(77, 25, 38);rgb(77, 64, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 53, 21);rgb(32, 64, 21);rgb(21, 64, 53);rgb(21, 32, 64);rgb(53, 21, 64);rgb(64, 21, 32);rgb(64, 53, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 45, 18);rgb(27, 54, 18);rgb(18, 54, 45);rgb(18, 27, 54);rgb(45, 18, 54);rgb(54, 18, 27);rgb(54, 45, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(960 719.98)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 64, 25);rgb(38, 77, 25);rgb(25, 77, 64);rgb(25, 38, 77);rgb(64, 25, 77);rgb(77, 25, 38);rgb(77, 64, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 53, 21);rgb(32, 64, 21);rgb(21, 64, 53);rgb(21, 32, 64);rgb(53, 21, 64);rgb(64, 21, 32);rgb(64, 53, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 45, 18);rgb(27, 54, 18);rgb(18, 54, 45);rgb(18, 27, 54);rgb(45, 18, 54);rgb(54, 18, 27);rgb(54, 45, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(940 725.57)"><animateTransform attributeName="transform" type="translate" values="940 731.53;940 725.57" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(115, 96, 38);rgb(57, 115, 38);rgb(38, 115, 96);rgb(38, 57, 115);rgb(96, 38, 115);rgb(115, 38, 57);rgb(115, 96, 38)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="7.76" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(96, 80, 32);rgb(48, 96, 32);rgb(32, 96, 80);rgb(32, 48, 96);rgb(80, 32, 96);rgb(96, 32, 48);rgb(96, 80, 32)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;7.76" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="7.76" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(80, 67, 27);rgb(40, 80, 27);rgb(27, 80, 67);rgb(27, 40, 80);rgb(67, 27, 80);rgb(80, 27, 40);rgb(80, 67, 27)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;7.76" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(920 737.12)"><animateTransform attributeName="transform" type="translate" values="920 743.08;920 737.12" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(115, 96, 38);rgb(57, 115, 38);rgb(38, 115, 96);rgb(38, 57, 115);rgb(96, 38, 115);rgb(115, 38, 57);rgb(115, 96, 38)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="7.76" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(96, 80, 32);rgb(48, 96, 32);rgb(32, 96, 80);rgb(32, 48, 96);rgb(80, 32, 96);rgb(96, 32, 48);rgb(96, 80, 32)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;7.76" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="7.76" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(80, 67, 27);rgb(40, 80, 27);rgb(27, 80, 67);rgb(27, 40, 80);rgb(67, 27, 80);rgb(80, 27, 40);rgb(80, 67, 27)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;7.76" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(1060 673.94)"><animateTransform attributeName="transform" type="translate" values="1060 685.34;1060 673.94" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(115, 87, 38);rgb(66, 115, 38);rgb(38, 115, 87);rgb(38, 66, 115);rgb(87, 38, 115);rgb(115, 38, 66);rgb(115, 87, 38)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="12.47" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(96, 73, 32);rgb(55, 96, 32);rgb(32, 96, 73);rgb(32, 55, 96);rgb(73, 32, 96);rgb(96, 32, 55);rgb(96, 73, 32)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;12.47" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="12.47" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(80, 61, 27);rgb(46, 80, 27);rgb(27, 80, 61);rgb(27, 46, 80);rgb(61, 27, 80);rgb(80, 27, 46);rgb(80, 61, 27)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;12.47" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(1040 685.49)"><animateTransform attributeName="transform" type="translate" values="1040 696.89;1040 685.49" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(115, 87, 38);rgb(66, 115, 38);rgb(38, 115, 87);rgb(38, 66, 115);rgb(87, 38, 115);rgb(115, 38, 66);rgb(115, 87, 38)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="12.47" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(96, 73, 32);rgb(55, 96, 32);rgb(32, 96, 73);rgb(32, 55, 96);rgb(73, 32, 96);rgb(96, 32, 55);rgb(96, 73, 32)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;12.47" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="12.47" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(80, 61, 27);rgb(46, 80, 27);rgb(27, 80, 61);rgb(27, 46, 80);rgb(61, 27, 80);rgb(80, 27, 46);rgb(80, 61, 27)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;12.47" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(1020 699.7)"><animateTransform attributeName="transform" type="translate" values="1020 708.44;1020 699.7" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(115, 87, 38);rgb(66, 115, 38);rgb(38, 115, 87);rgb(38, 66, 115);rgb(87, 38, 115);rgb(115, 38, 66);rgb(115, 87, 38)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="10.17" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(96, 73, 32);rgb(55, 96, 32);rgb(32, 96, 73);rgb(32, 55, 96);rgb(73, 32, 96);rgb(96, 32, 55);rgb(96, 73, 32)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;10.17" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="10.17" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(80, 61, 27);rgb(46, 80, 27);rgb(27, 80, 61);rgb(27, 46, 80);rgb(61, 27, 80);rgb(80, 27, 46);rgb(80, 61, 27)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;10.17" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(1000 719.98)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 58, 25);rgb(44, 77, 25);rgb(25, 77, 58);rgb(25, 44, 77);rgb(58, 25, 77);rgb(77, 25, 44);rgb(77, 58, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 48, 21);rgb(37, 64, 21);rgb(21, 64, 48);rgb(21, 37, 64);rgb(48, 21, 64);rgb(64, 21, 37);rgb(64, 48, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 40, 18);rgb(31, 54, 18);rgb(18, 54, 40);rgb(18, 31, 54);rgb(40, 18, 54);rgb(54, 18, 31);rgb(54, 40, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(980 731.53)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 58, 25);rgb(44, 77, 25);rgb(25, 77, 58);rgb(25, 44, 77);rgb(58, 25, 77);rgb(77, 25, 44);rgb(77, 58, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 48, 21);rgb(37, 64, 21);rgb(21, 64, 48);rgb(21, 37, 64);rgb(48, 21, 64);rgb(64, 21, 37);rgb(64, 48, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 40, 18);rgb(31, 54, 18);rgb(18, 54, 40);rgb(18, 31, 54);rgb(40, 18, 54);rgb(54, 18, 31);rgb(54, 40, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(960 743.08)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 58, 25);rgb(44, 77, 25);rgb(25, 77, 58);rgb(25, 44, 77);rgb(58, 25, 77);rgb(77, 25, 44);rgb(77, 58, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 48, 21);rgb(37, 64, 21);rgb(21, 64, 48);rgb(21, 37, 64);rgb(48, 21, 64);rgb(64, 21, 37);rgb(64, 48, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 40, 18);rgb(31, 54, 18);rgb(18, 54, 40);rgb(18, 31, 54);rgb(40, 18, 54);rgb(54, 18, 31);rgb(54, 40, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(940 754.62)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 58, 25);rgb(44, 77, 25);rgb(25, 77, 58);rgb(25, 44, 77);rgb(58, 25, 77);rgb(77, 25, 44);rgb(77, 58, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 48, 21);rgb(37, 64, 21);rgb(21, 64, 48);rgb(21, 37, 64);rgb(48, 21, 64);rgb(64, 21, 37);rgb(64, 48, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 40, 18);rgb(31, 54, 18);rgb(18, 54, 40);rgb(18, 31, 54);rgb(40, 18, 54);rgb(54, 18, 31);rgb(54, 40, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(1080 696.89)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 52, 25);rgb(50, 77, 25);rgb(25, 77, 52);rgb(25, 50, 77);rgb(52, 25, 77);rgb(77, 25, 50);rgb(77, 52, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 43, 21);rgb(42, 64, 21);rgb(21, 64, 43);rgb(21, 42, 64);rgb(43, 21, 64);rgb(64, 21, 42);rgb(64, 43, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 36, 18);rgb(35, 54, 18);rgb(18, 54, 36);rgb(18, 35, 54);rgb(36, 18, 54);rgb(54, 18, 35);rgb(54, 36, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(1060 708.44)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 52, 25);rgb(50, 77, 25);rgb(25, 77, 52);rgb(25, 50, 77);rgb(52, 25, 77);rgb(77, 25, 50);rgb(77, 52, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 43, 21);rgb(42, 64, 21);rgb(21, 64, 43);rgb(21, 42, 64);rgb(43, 21, 64);rgb(64, 21, 42);rgb(64, 43, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 36, 18);rgb(35, 54, 18);rgb(18, 54, 36);rgb(18, 35, 54);rgb(36, 18, 54);rgb(54, 18, 35);rgb(54, 36, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(1040 719.98)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 52, 25);rgb(50, 77, 25);rgb(25, 77, 52);rgb(25, 50, 77);rgb(52, 25, 77);rgb(77, 25, 50);rgb(77, 52, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 43, 21);rgb(42, 64, 21);rgb(21, 64, 43);rgb(21, 42, 64);rgb(43, 21, 64);rgb(64, 21, 42);rgb(64, 43, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 36, 18);rgb(35, 54, 18);rgb(18, 54, 36);rgb(18, 35, 54);rgb(36, 18, 54);rgb(54, 18, 35);rgb(54, 36, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(1020 731.53)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 52, 25);rgb(50, 77, 25);rgb(25, 77, 52);rgb(25, 50, 77);rgb(52, 25, 77);rgb(77, 25, 50);rgb(77, 52, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 43, 21);rgb(42, 64, 21);rgb(21, 64, 43);rgb(21, 42, 64);rgb(43, 21, 64);rgb(64, 21, 42);rgb(64, 43, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 36, 18);rgb(35, 54, 18);rgb(18, 54, 36);rgb(18, 35, 54);rgb(36, 18, 54);rgb(54, 18, 35);rgb(54, 36, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(1000 743.08)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 52, 25);rgb(50, 77, 25);rgb(25, 77, 52);rgb(25, 50, 77);rgb(52, 25, 77);rgb(77, 25, 50);rgb(77, 52, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 43, 21);rgb(42, 64, 21);rgb(21, 64, 43);rgb(21, 42, 64);rgb(43, 21, 64);rgb(64, 21, 42);rgb(64, 43, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 36, 18);rgb(35, 54, 18);rgb(18, 54, 36);rgb(18, 35, 54);rgb(36, 18, 54);rgb(54, 18, 35);rgb(54, 36, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(980 754.62)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 52, 25);rgb(50, 77, 25);rgb(25, 77, 52);rgb(25, 50, 77);rgb(52, 25, 77);rgb(77, 25, 50);rgb(77, 52, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 43, 21);rgb(42, 64, 21);rgb(21, 64, 43);rgb(21, 42, 64);rgb(43, 21, 64);rgb(64, 21, 42);rgb(64, 43, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 36, 18);rgb(35, 54, 18);rgb(18, 54, 36);rgb(18, 35, 54);rgb(36, 18, 54);rgb(54, 18, 35);rgb(54, 36, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(960 766.17)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 52, 25);rgb(50, 77, 25);rgb(25, 77, 52);rgb(25, 50, 77);rgb(52, 25, 77);rgb(77, 25, 50);rgb(77, 52, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 43, 21);rgb(42, 64, 21);rgb(21, 64, 43);rgb(21, 42, 64);rgb(43, 21, 64);rgb(64, 21, 42);rgb(64, 43, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 36, 18);rgb(35, 54, 18);rgb(18, 54, 36);rgb(18, 35, 54);rgb(36, 18, 54);rgb(54, 18, 35);rgb(54, 36, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(1100 708.44)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 46, 25);rgb(56, 77, 25);rgb(25, 77, 46);rgb(25, 56, 77);rgb(46, 25, 77);rgb(77, 25, 56);rgb(77, 46, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 38, 21);rgb(47, 64, 21);rgb(21, 64, 38);rgb(21, 47, 64);rgb(38, 21, 64);rgb(64, 21, 47);rgb(64, 38, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 32, 18);rgb(39, 54, 18);rgb(18, 54, 32);rgb(18, 39, 54);rgb(32, 18, 54);rgb(54, 18, 39);rgb(54, 32, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(1080 719.98)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 46, 25);rgb(56, 77, 25);rgb(25, 77, 46);rgb(25, 56, 77);rgb(46, 25, 77);rgb(77, 25, 56);rgb(77, 46, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 38, 21);rgb(47, 64, 21);rgb(21, 64, 38);rgb(21, 47, 64);rgb(38, 21, 64);rgb(64, 21, 47);rgb(64, 38, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 32, 18);rgb(39, 54, 18);rgb(18, 54, 32);rgb(18, 39, 54);rgb(32, 18, 54);rgb(54, 18, 39);rgb(54, 32, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(1060 731.53)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 46, 25);rgb(56, 77, 25);rgb(25, 77, 46);rgb(25, 56, 77);rgb(46, 25, 77);rgb(77, 25, 56);rgb(77, 46, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 38, 21);rgb(47, 64, 21);rgb(21, 64, 38);rgb(21, 47, 64);rgb(38, 21, 64);rgb(64, 21, 47);rgb(64, 38, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 32, 18);rgb(39, 54, 18);rgb(18, 54, 32);rgb(18, 39, 54);rgb(32, 18, 54);rgb(54, 18, 39);rgb(54, 32, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(1040 743.08)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 46, 25);rgb(56, 77, 25);rgb(25, 77, 46);rgb(25, 56, 77);rgb(46, 25, 77);rgb(77, 25, 56);rgb(77, 46, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 38, 21);rgb(47, 64, 21);rgb(21, 64, 38);rgb(21, 47, 64);rgb(38, 21, 64);rgb(64, 21, 47);rgb(64, 38, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 32, 18);rgb(39, 54, 18);rgb(18, 54, 32);rgb(18, 39, 54);rgb(32, 18, 54);rgb(54, 18, 39);rgb(54, 32, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(1020 754.62)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 46, 25);rgb(56, 77, 25);rgb(25, 77, 46);rgb(25, 56, 77);rgb(46, 25, 77);rgb(77, 25, 56);rgb(77, 46, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 38, 21);rgb(47, 64, 21);rgb(21, 64, 38);rgb(21, 47, 64);rgb(38, 21, 64);rgb(64, 21, 47);rgb(64, 38, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 32, 18);rgb(39, 54, 18);rgb(18, 54, 32);rgb(18, 39, 54);rgb(32, 18, 54);rgb(54, 18, 39);rgb(54, 32, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(1000 766.17)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 46, 25);rgb(56, 77, 25);rgb(25, 77, 46);rgb(25, 56, 77);rgb(46, 25, 77);rgb(77, 25, 56);rgb(77, 46, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 38, 21);rgb(47, 64, 21);rgb(21, 64, 38);rgb(21, 47, 64);rgb(38, 21, 64);rgb(64, 21, 47);rgb(64, 38, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 32, 18);rgb(39, 54, 18);rgb(18, 54, 32);rgb(18, 39, 54);rgb(32, 18, 54);rgb(54, 18, 39);rgb(54, 32, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(980 777.72)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 46, 25);rgb(56, 77, 25);rgb(25, 77, 46);rgb(25, 56, 77);rgb(46, 25, 77);rgb(77, 25, 56);rgb(77, 46, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 38, 21);rgb(47, 64, 21);rgb(21, 64, 38);rgb(21, 47, 64);rgb(38, 21, 64);rgb(64, 21, 47);rgb(64, 38, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 32, 18);rgb(39, 54, 18);rgb(18, 54, 32);rgb(18, 39, 54);rgb(32, 18, 54);rgb(54, 18, 39);rgb(54, 32, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(1120 719.98)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 40, 25);rgb(62, 77, 25);rgb(25, 77, 40);rgb(25, 62, 77);rgb(40, 25, 77);rgb(77, 25, 62);rgb(77, 40, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 33, 21);rgb(52, 64, 21);rgb(21, 64, 33);rgb(21, 52, 64);rgb(33, 21, 64);rgb(64, 21, 52);rgb(64, 33, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 28, 18);rgb(43, 54, 18);rgb(18, 54, 28);rgb(18, 43, 54);rgb(28, 18, 54);rgb(54, 18, 43);rgb(54, 28, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(1100 731.53)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 40, 25);rgb(62, 77, 25);rgb(25, 77, 40);rgb(25, 62, 77);rgb(40, 25, 77);rgb(77, 25, 62);rgb(77, 40, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 33, 21);rgb(52, 64, 21);rgb(21, 64, 33);rgb(21, 52, 64);rgb(33, 21, 64);rgb(64, 21, 52);rgb(64, 33, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 28, 18);rgb(43, 54, 18);rgb(18, 54, 28);rgb(18, 43, 54);rgb(28, 18, 54);rgb(54, 18, 43);rgb(54, 28, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(1080 743.08)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 40, 25);rgb(62, 77, 25);rgb(25, 77, 40);rgb(25, 62, 77);rgb(40, 25, 77);rgb(77, 25, 62);rgb(77, 40, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 33, 21);rgb(52, 64, 21);rgb(21, 64, 33);rgb(21, 52, 64);rgb(33, 21, 64);rgb(64, 21, 52);rgb(64, 33, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 28, 18);rgb(43, 54, 18);rgb(18, 54, 28);rgb(18, 43, 54);rgb(28, 18, 54);rgb(54, 18, 43);rgb(54, 28, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(1060 751.57)"><animateTransform attributeName="transform" type="translate" values="1060 754.62;1060 751.57" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(115, 60, 38);rgb(93, 115, 38);rgb(38, 115, 60);rgb(38, 93, 115);rgb(60, 38, 115);rgb(115, 38, 93);rgb(115, 60, 38)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="5.24" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(96, 50, 32);rgb(78, 96, 32);rgb(32, 96, 50);rgb(32, 78, 96);rgb(50, 32, 96);rgb(96, 32, 78);rgb(96, 50, 32)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;5.24" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="5.24" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(80, 42, 27);rgb(65, 80, 27);rgb(27, 80, 42);rgb(27, 65, 80);rgb(42, 27, 80);rgb(80, 27, 65);rgb(80, 42, 27)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;5.24" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(1040 719.77)"><animateTransform attributeName="transform" type="translate" values="1040 766.17;1040 719.77" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(191, 100, 64);rgb(155, 191, 64);rgb(64, 191, 100);rgb(64, 155, 191);rgb(100, 64, 191);rgb(191, 64, 155);rgb(191, 100, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="42.78" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(160, 84, 53);rgb(130, 160, 53);rgb(53, 160, 84);rgb(53, 130, 160);rgb(84, 53, 160);rgb(160, 53, 130);rgb(160, 84, 53)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;42.78" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="42.78" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(134, 70, 45);rgb(109, 134, 45);rgb(45, 134, 70);rgb(45, 109, 134);rgb(70, 45, 134);rgb(134, 45, 109);rgb(134, 70, 45)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;42.78" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(1020 742.72)"><animateTransform attributeName="transform" type="translate" values="1020 777.72;1020 742.72" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(153, 80, 51);rgb(124, 153, 51);rgb(51, 153, 80);rgb(51, 124, 153);rgb(80, 51, 153);rgb(153, 51, 124);rgb(153, 80, 51)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="32.91" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(128, 67, 43);rgb(104, 128, 43);rgb(43, 128, 67);rgb(43, 104, 128);rgb(67, 43, 128);rgb(128, 43, 104);rgb(128, 67, 43)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;32.91" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="32.91" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(107, 56, 36);rgb(87, 107, 36);rgb(36, 107, 56);rgb(36, 87, 107);rgb(56, 36, 107);rgb(107, 36, 87);rgb(107, 56, 36)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;32.91" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(1000 789.26)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 40, 25);rgb(62, 77, 25);rgb(25, 77, 40);rgb(25, 62, 77);rgb(40, 25, 77);rgb(77, 25, 62);rgb(77, 40, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 33, 21);rgb(52, 64, 21);rgb(21, 64, 33);rgb(21, 52, 64);rgb(33, 21, 64);rgb(64, 21, 52);rgb(64, 33, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 28, 18);rgb(43, 54, 18);rgb(18, 54, 28);rgb(18, 43, 54);rgb(28, 18, 54);rgb(54, 18, 43);rgb(54, 28, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(1140 722.79)"><animateTransform attributeName="transform" type="translate" values="1140 731.53;1140 722.79" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(115, 51, 38);rgb(102, 115, 38);rgb(38, 115, 51);rgb(38, 102, 115);rgb(51, 38, 115);rgb(115, 38, 102);rgb(115, 51, 38)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="10.17" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(96, 43, 32);rgb(85, 96, 32);rgb(32, 96, 43);rgb(32, 85, 96);rgb(43, 32, 96);rgb(96, 32, 85);rgb(96, 43, 32)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;10.17" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="10.17" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(80, 36, 27);rgb(71, 80, 27);rgb(27, 80, 36);rgb(27, 71, 80);rgb(36, 27, 80);rgb(80, 27, 71);rgb(80, 36, 27)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;10.17" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(1120 708.08)"><animateTransform attributeName="transform" type="translate" values="1120 743.08;1120 708.08" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(153, 68, 51);rgb(136, 153, 51);rgb(51, 153, 68);rgb(51, 136, 153);rgb(68, 51, 153);rgb(153, 51, 136);rgb(153, 68, 51)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="32.91" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(128, 57, 43);rgb(114, 128, 43);rgb(43, 128, 57);rgb(43, 114, 128);rgb(57, 43, 128);rgb(128, 43, 114);rgb(128, 57, 43)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;32.91" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="32.91" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(107, 48, 36);rgb(95, 107, 36);rgb(36, 107, 48);rgb(36, 95, 107);rgb(48, 36, 107);rgb(107, 36, 95);rgb(107, 48, 36)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;32.91" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(1100 754.62)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 34, 25);rgb(68, 77, 25);rgb(25, 77, 34);rgb(25, 68, 77);rgb(34, 25, 77);rgb(77, 25, 68);rgb(77, 34, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 28, 21);rgb(57, 64, 21);rgb(21, 64, 28);rgb(21, 57, 64);rgb(28, 21, 64);rgb(64, 21, 57);rgb(64, 28, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 24, 18);rgb(48, 54, 18);rgb(18, 54, 24);rgb(18, 48, 54);rgb(24, 18, 54);rgb(54, 18, 48);rgb(54, 24, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(1080 754.77)"><animateTransform attributeName="transform" type="translate" values="1080 766.17;1080 754.77" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(115, 51, 38);rgb(102, 115, 38);rgb(38, 115, 51);rgb(38, 102, 115);rgb(51, 38, 115);rgb(115, 38, 102);rgb(115, 51, 38)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="12.47" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(96, 43, 32);rgb(85, 96, 32);rgb(32, 96, 43);rgb(32, 85, 96);rgb(43, 32, 96);rgb(96, 32, 85);rgb(96, 43, 32)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;12.47" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="12.47" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(80, 36, 27);rgb(71, 80, 27);rgb(27, 80, 36);rgb(27, 71, 80);rgb(36, 27, 80);rgb(80, 27, 71);rgb(80, 36, 27)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;12.47" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(1060 756.68)"><animateTransform attributeName="transform" type="translate" values="1060 777.72;1060 756.68" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(134, 60, 45);rgb(119, 134, 45);rgb(45, 134, 60);rgb(45, 119, 134);rgb(60, 45, 134);rgb(134, 45, 119);rgb(134, 60, 45)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="20.82" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(112, 50, 37);rgb(100, 112, 37);rgb(37, 112, 50);rgb(37, 100, 112);rgb(50, 37, 112);rgb(112, 37, 100);rgb(112, 50, 37)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;20.82" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="20.82" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(94, 42, 31);rgb(83, 94, 31);rgb(31, 94, 42);rgb(31, 83, 94);rgb(42, 31, 94);rgb(94, 31, 83);rgb(94, 42, 31)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;20.82" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(1040 789.26)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 34, 25);rgb(68, 77, 25);rgb(25, 77, 34);rgb(25, 68, 77);rgb(34, 25, 77);rgb(77, 25, 68);rgb(77, 34, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 28, 21);rgb(57, 64, 21);rgb(21, 64, 28);rgb(21, 57, 64);rgb(28, 21, 64);rgb(64, 21, 57);rgb(64, 28, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 24, 18);rgb(48, 54, 18);rgb(18, 54, 24);rgb(18, 48, 54);rgb(24, 18, 54);rgb(54, 18, 48);rgb(54, 24, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(1020 800.81)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 34, 25);rgb(68, 77, 25);rgb(25, 77, 34);rgb(25, 68, 77);rgb(34, 25, 77);rgb(77, 25, 68);rgb(77, 34, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 28, 21);rgb(57, 64, 21);rgb(21, 64, 28);rgb(21, 57, 64);rgb(28, 21, 64);rgb(64, 21, 57);rgb(64, 28, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 24, 18);rgb(48, 54, 18);rgb(18, 54, 24);rgb(18, 48, 54);rgb(24, 18, 54);rgb(54, 18, 48);rgb(54, 24, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(1160 731.67)"><animateTransform attributeName="transform" type="translate" values="1160 743.08;1160 731.67" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(115, 42, 38);rgb(111, 115, 38);rgb(38, 115, 42);rgb(38, 111, 115);rgb(42, 38, 115);rgb(115, 38, 111);rgb(115, 42, 38)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="12.47" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(96, 35, 32);rgb(93, 96, 32);rgb(32, 96, 35);rgb(32, 93, 96);rgb(35, 32, 96);rgb(96, 32, 93);rgb(96, 35, 32)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;12.47" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="12.47" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(80, 29, 27);rgb(78, 80, 27);rgb(27, 80, 29);rgb(27, 78, 80);rgb(29, 27, 80);rgb(80, 27, 78);rgb(80, 29, 27)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;12.47" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(1140 706.75)"><animateTransform attributeName="transform" type="translate" values="1140 754.62;1140 706.75" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(191, 70, 64);rgb(185, 191, 64);rgb(64, 191, 70);rgb(64, 185, 191);rgb(70, 64, 191);rgb(191, 64, 185);rgb(191, 70, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="44.06" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(160, 59, 53);rgb(155, 160, 53);rgb(53, 160, 59);rgb(53, 155, 160);rgb(59, 53, 160);rgb(160, 53, 155);rgb(160, 59, 53)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;44.06" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="44.06" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(134, 49, 45);rgb(129, 134, 45);rgb(45, 134, 49);rgb(45, 129, 134);rgb(49, 45, 134);rgb(134, 45, 129);rgb(134, 49, 45)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;44.06" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(1120 715.46)"><animateTransform attributeName="transform" type="translate" values="1120 766.17;1120 715.46" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(191, 70, 64);rgb(185, 191, 64);rgb(64, 191, 70);rgb(64, 185, 191);rgb(70, 64, 191);rgb(191, 64, 185);rgb(191, 70, 64)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="46.52" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(160, 59, 53);rgb(155, 160, 53);rgb(53, 160, 59);rgb(53, 155, 160);rgb(59, 53, 160);rgb(160, 53, 155);rgb(160, 59, 53)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;46.52" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="46.52" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(134, 49, 45);rgb(129, 134, 45);rgb(45, 134, 49);rgb(45, 129, 134);rgb(49, 45, 134);rgb(134, 45, 129);rgb(134, 49, 45)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;46.52" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(1100 758.95)"><animateTransform attributeName="transform" type="translate" values="1100 777.72;1100 758.95" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(134, 49, 45);rgb(129, 134, 45);rgb(45, 134, 49);rgb(45, 129, 134);rgb(49, 45, 134);rgb(134, 45, 129);rgb(134, 49, 45)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="18.85" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(112, 41, 37);rgb(108, 112, 37);rgb(37, 112, 41);rgb(37, 108, 112);rgb(41, 37, 112);rgb(112, 37, 108);rgb(112, 41, 37)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;18.85" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="18.85" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(94, 34, 31);rgb(91, 94, 31);rgb(31, 94, 34);rgb(31, 91, 94);rgb(34, 31, 94);rgb(94, 31, 91);rgb(94, 34, 31)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;18.85" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(1080 789.26)"><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(77, 28, 25);rgb(74, 77, 25);rgb(25, 77, 28);rgb(25, 74, 77);rgb(28, 25, 77);rgb(77, 25, 74);rgb(77, 28, 25)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(64, 23, 21);rgb(62, 64, 21);rgb(21, 64, 23);rgb(21, 62, 64);rgb(23, 21, 64);rgb(64, 21, 62);rgb(64, 23, 21)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="2.6" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(54, 20, 18);rgb(52, 54, 18);rgb(18, 54, 20);rgb(18, 52, 54);rgb(20, 18, 54);rgb(54, 18, 52);rgb(54, 20, 18)" dur="10s" repeatCount="indefinite"></animate></rect></g><g transform="translate(1060 789.41)"><animateTransform attributeName="transform" type="translate" values="1060 800.81;1060 789.41" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(115, 42, 38);rgb(111, 115, 38);rgb(38, 115, 42);rgb(38, 111, 115);rgb(42, 38, 115);rgb(115, 38, 111);rgb(115, 42, 38)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="12.47" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(96, 35, 32);rgb(93, 96, 32);rgb(32, 96, 35);rgb(32, 93, 96);rgb(35, 32, 96);rgb(96, 32, 93);rgb(96, 35, 32)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;12.47" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="12.47" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(80, 29, 27);rgb(78, 80, 27);rgb(27, 80, 29);rgb(27, 78, 80);rgb(29, 27, 80);rgb(80, 27, 78);rgb(80, 29, 27)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;12.47" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(1040 809.31)"><animateTransform attributeName="transform" type="translate" values="1040 812.36;1040 809.31" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(115, 42, 38);rgb(111, 115, 38);rgb(38, 115, 42);rgb(38, 111, 115);rgb(42, 38, 115);rgb(115, 38, 111);rgb(115, 42, 38)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="5.24" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(96, 35, 32);rgb(93, 96, 32);rgb(32, 96, 35);rgb(32, 93, 96);rgb(35, 32, 96);rgb(96, 32, 93);rgb(96, 35, 32)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;5.24" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="5.24" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(80, 29, 27);rgb(78, 80, 27);rgb(27, 80, 29);rgb(27, 78, 80);rgb(29, 27, 80);rgb(80, 27, 78);rgb(80, 29, 27)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;5.24" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(1180 745.88)"><animateTransform attributeName="transform" type="translate" values="1180 754.62;1180 745.88" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(115, 38, 43);rgb(115, 110, 38);rgb(43, 115, 38);rgb(38, 115, 110);rgb(38, 43, 115);rgb(110, 38, 115);rgb(115, 38, 43)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="10.17" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(96, 32, 36);rgb(96, 92, 32);rgb(36, 96, 32);rgb(32, 96, 92);rgb(32, 36, 96);rgb(92, 32, 96);rgb(96, 32, 36)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;10.17" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="10.17" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(80, 27, 30);rgb(80, 77, 27);rgb(30, 80, 27);rgb(27, 80, 77);rgb(27, 30, 80);rgb(77, 27, 80);rgb(80, 27, 30)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;10.17" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(1160 754.77)"><animateTransform attributeName="transform" type="translate" values="1160 766.17;1160 754.77" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(115, 38, 43);rgb(115, 110, 38);rgb(43, 115, 38);rgb(38, 115, 110);rgb(38, 43, 115);rgb(110, 38, 115);rgb(115, 38, 43)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="12.47" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(96, 32, 36);rgb(96, 92, 32);rgb(36, 96, 32);rgb(32, 96, 92);rgb(32, 36, 96);rgb(92, 32, 96);rgb(96, 32, 36)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;12.47" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="12.47" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(80, 27, 30);rgb(80, 77, 27);rgb(30, 80, 27);rgb(27, 80, 77);rgb(27, 30, 80);rgb(77, 27, 80);rgb(80, 27, 30)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;12.47" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(1140 761.31)"><animateTransform attributeName="transform" type="translate" values="1140 777.72;1140 761.31" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(115, 38, 43);rgb(115, 110, 38);rgb(43, 115, 38);rgb(38, 115, 110);rgb(38, 43, 115);rgb(110, 38, 115);rgb(115, 38, 43)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="16.81" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(96, 32, 36);rgb(96, 92, 32);rgb(36, 96, 32);rgb(32, 96, 92);rgb(32, 36, 96);rgb(92, 32, 96);rgb(96, 32, 36)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;16.81" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="16.81" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(80, 27, 30);rgb(80, 77, 27);rgb(30, 80, 27);rgb(27, 80, 77);rgb(27, 30, 80);rgb(77, 27, 80);rgb(80, 27, 30)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;16.81" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(1120 777.86)"><animateTransform attributeName="transform" type="translate" values="1120 789.26;1120 777.86" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(115, 38, 43);rgb(115, 110, 38);rgb(43, 115, 38);rgb(38, 115, 110);rgb(38, 43, 115);rgb(110, 38, 115);rgb(115, 38, 43)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="12.47" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(96, 32, 36);rgb(96, 92, 32);rgb(36, 96, 32);rgb(32, 96, 92);rgb(32, 36, 96);rgb(92, 32, 96);rgb(96, 32, 36)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;12.47" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="12.47" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(80, 27, 30);rgb(80, 77, 27);rgb(30, 80, 27);rgb(27, 80, 77);rgb(27, 30, 80);rgb(77, 27, 80);rgb(80, 27, 30)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;12.47" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(1100 792.07)"><animateTransform attributeName="transform" type="translate" values="1100 800.81;1100 792.07" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(115, 38, 43);rgb(115, 110, 38);rgb(43, 115, 38);rgb(38, 115, 110);rgb(38, 43, 115);rgb(110, 38, 115);rgb(115, 38, 43)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="10.17" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(96, 32, 36);rgb(96, 92, 32);rgb(36, 96, 32);rgb(32, 96, 92);rgb(32, 36, 96);rgb(92, 32, 96);rgb(96, 32, 36)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;10.17" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="10.17" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(80, 27, 30);rgb(80, 77, 27);rgb(30, 80, 27);rgb(27, 80, 77);rgb(27, 30, 80);rgb(77, 27, 80);rgb(80, 27, 30)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;10.17" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(1080 798.4)"><animateTransform attributeName="transform" type="translate" values="1080 812.36;1080 798.4" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(115, 38, 43);rgb(115, 110, 38);rgb(43, 115, 38);rgb(38, 115, 110);rgb(38, 43, 115);rgb(110, 38, 115);rgb(115, 38, 43)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="14.68" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(96, 32, 36);rgb(96, 92, 32);rgb(36, 96, 32);rgb(32, 96, 92);rgb(32, 36, 96);rgb(92, 32, 96);rgb(96, 32, 36)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;14.68" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="14.68" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(80, 27, 30);rgb(80, 77, 27);rgb(30, 80, 27);rgb(27, 80, 77);rgb(27, 30, 80);rgb(77, 27, 80);rgb(80, 27, 30)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;14.68" dur="3s" repeatCount="1"></animate></rect></g><g transform="translate(1060 773.19)"><animateTransform attributeName="transform" type="translate" values="1060 823.91;1060 773.19" dur="3s" repeatCount="1"></animateTransform><rect stroke="none" x="0" y="0" width="18" height="18" transform="skewY(-30) skewX(40.89) scale(1 1.15)"><animate attributeName="fill" values="rgb(191, 64, 72);rgb(191, 183, 64);rgb(72, 191, 64);rgb(64, 191, 183);rgb(64, 72, 191);rgb(183, 64, 191);rgb(191, 64, 72)" dur="10s" repeatCount="indefinite"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="46.52" transform="skewY(30) scale(1 1.15)"><animate attributeName="fill" values="rgb(160, 53, 60);rgb(160, 153, 53);rgb(60, 160, 53);rgb(53, 160, 153);rgb(53, 60, 160);rgb(153, 53, 160);rgb(160, 53, 60)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;46.52" dur="3s" repeatCount="1"></animate></rect><rect stroke="none" x="0" y="0" width="18" height="46.52" transform="translate(18 10.39) skewY(-30) scale(1 1.15)"><animate attributeName="fill" values="rgb(134, 45, 51);rgb(134, 128, 45);rgb(51, 134, 45);rgb(45, 134, 128);rgb(45, 51, 134);rgb(128, 45, 134);rgb(134, 45, 51)" dur="10s" repeatCount="indefinite"></animate><animate attributeName="height" values="2.6;46.52" dur="3s" repeatCount="1"></animate></rect></g></g><g transform="translate(980, 284.5)"><line x1="0" y1="-31.2" x2="29.67" y2="-9.64" class="stroke-weak" style="stroke-dasharray: 4 4; stroke-width: 1px;"></line><line x1="29.67" y1="-9.64" x2="18.34" y2="25.24" class="stroke-weak" style="stroke-dasharray: 4 4; stroke-width: 1px;"></line><line x1="18.34" y1="25.24" x2="-18.34" y2="25.24" class="stroke-weak" style="stroke-dasharray: 4 4; stroke-width: 1px;"></line><line x1="-18.34" y1="25.24" x2="-29.67" y2="-9.64" class="stroke-weak" style="stroke-dasharray: 4 4; stroke-width: 1px;"></line><line x1="-29.67" y1="-9.64" x2="0" y2="-31.2" class="stroke-weak" style="stroke-dasharray: 4 4; stroke-width: 1px;"></line><line x1="0" y1="-62.4" x2="59.35" y2="-19.28" class="stroke-weak" style="stroke-dasharray: 4 4; stroke-width: 1px;"></line><line x1="59.35" y1="-19.28" x2="36.68" y2="50.48" class="stroke-weak" style="stroke-dasharray: 4 4; stroke-width: 1px;"></line><line x1="36.68" y1="50.48" x2="-36.68" y2="50.48" class="stroke-weak" style="stroke-dasharray: 4 4; stroke-width: 1px;"></line><line x1="-36.68" y1="50.48" x2="-59.35" y2="-19.28" class="stroke-weak" style="stroke-dasharray: 4 4; stroke-width: 1px;"></line><line x1="-59.35" y1="-19.28" x2="0" y2="-62.4" class="stroke-weak" style="stroke-dasharray: 4 4; stroke-width: 1px;"></line><line x1="0" y1="-93.6" x2="89.02" y2="-28.92" class="stroke-weak" style="stroke-dasharray: 4 4; stroke-width: 1px;"></line><line x1="89.02" y1="-28.92" x2="55.02" y2="75.72" class="stroke-weak" style="stroke-dasharray: 4 4; stroke-width: 1px;"></line><line x1="55.02" y1="75.72" x2="-55.02" y2="75.72" class="stroke-weak" style="stroke-dasharray: 4 4; stroke-width: 1px;"></line><line x1="-55.02" y1="75.72" x2="-89.02" y2="-28.92" class="stroke-weak" style="stroke-dasharray: 4 4; stroke-width: 1px;"></line><line x1="-89.02" y1="-28.92" x2="0" y2="-93.6" class="stroke-weak" style="stroke-dasharray: 4 4; stroke-width: 1px;"></line><line x1="0" y1="-124.8" x2="118.69" y2="-38.57" class="stroke-weak" style="stroke-dasharray: 4 4; stroke-width: 1px;"></line><line x1="118.69" y1="-38.57" x2="73.36" y2="100.97" class="stroke-weak" style="stroke-dasharray: 4 4; stroke-width: 1px;"></line><line x1="73.36" y1="100.97" x2="-73.36" y2="100.97" class="stroke-weak" style="stroke-dasharray: 4 4; stroke-width: 1px;"></line><line x1="-73.36" y1="100.97" x2="-118.69" y2="-38.57" class="stroke-weak" style="stroke-dasharray: 4 4; stroke-width: 1px;"></line><line x1="-118.69" y1="-38.57" x2="0" y2="-124.8" class="stroke-weak" style="stroke-dasharray: 4 4; stroke-width: 1px;"></line><line x1="0" y1="-156" x2="148.36" y2="-48.21" class="stroke-weak" style="stroke-dasharray: 4 4; stroke-width: 1px;"></line><line x1="148.36" y1="-48.21" x2="91.69" y2="126.21" class="stroke-weak" style="stroke-dasharray: 4 4; stroke-width: 1px;"></line><line x1="91.69" y1="126.21" x2="-91.69" y2="126.21" class="stroke-weak" style="stroke-dasharray: 4 4; stroke-width: 1px;"></line><line x1="-91.69" y1="126.21" x2="-148.36" y2="-48.21" class="stroke-weak" style="stroke-dasharray: 4 4; stroke-width: 1px;"></line><line x1="-148.36" y1="-48.21" x2="0" y2="-156" class="stroke-weak" style="stroke-dasharray: 4 4; stroke-width: 1px;"></line><text style="font-size: 13px;" text-anchor="start" dominant-baseline="auto" x="3.12" y="-31.2" class="fill-weak">1</text><text style="font-size: 13px;" text-anchor="start" dominant-baseline="auto" x="3.12" y="-62.4" class="fill-weak">10</text><text style="font-size: 13px;" text-anchor="start" dominant-baseline="auto" x="3.12" y="-93.6" class="fill-weak">100</text><text style="font-size: 13px;" text-anchor="start" dominant-baseline="auto" x="3.12" y="-124.8" class="fill-weak">1K</text><text style="font-size: 13px;" text-anchor="start" dominant-baseline="auto" x="3.12" y="-156" class="fill-weak">10K</text><g class="axis"><line x1="0" y1="-31.2" x2="0" y2="-156" class="stroke-weak" style="stroke-dasharray: 4 4; stroke-width: 1px;"></line><text style="font-size: 20.8px;" text-anchor="middle" dominant-baseline="middle" x="0" y="-182.52" class="fill-fg">Commit<title>441</title></text></g><g class="axis"><line x1="29.67" y1="-9.64" x2="148.36" y2="-48.21" class="stroke-weak" style="stroke-dasharray: 4 4; stroke-width: 1px;"></line><text style="font-size: 20.8px;" text-anchor="middle" dominant-baseline="middle" x="185.46" y="-56.4" class="fill-fg">Issue<title>0</title></text></g><g class="axis"><line x1="18.34" y1="25.24" x2="91.69" y2="126.21" class="stroke-weak" style="stroke-dasharray: 4 4; stroke-width: 1px;"></line><text style="font-size: 20.8px;" text-anchor="middle" dominant-baseline="middle" x="114.62" y="147.66" class="fill-fg">PullReq<title>3</title></text></g><g class="axis"><line x1="-18.34" y1="25.24" x2="-91.69" y2="126.21" class="stroke-weak" style="stroke-dasharray: 4 4; stroke-width: 1px;"></line><text style="font-size: 20.8px;" text-anchor="middle" dominant-baseline="middle" x="-114.62" y="147.66" class="fill-fg">Review<title>0</title></text></g><g class="axis"><line x1="-29.67" y1="-9.64" x2="-148.36" y2="-48.21" class="stroke-weak" style="stroke-dasharray: 4 4; stroke-width: 1px;"></line><text style="font-size: 20.8px;" text-anchor="middle" dominant-baseline="middle" x="-185.46" y="-56.4" class="fill-fg">Repo<title>19</title></text></g><polygon class="radar" points="0,-113.71 23.74,-7.71 27.09,37.28 -14.67,20.19 -67.62,-21.97"><animate attributeName="points" values="0,-24.96 23.74,-7.71 14.67,20.19 -14.67,20.19 -23.74,-7.71;0,-113.71 23.74,-7.71 27.09,37.28 -14.67,20.19 -67.62,-21.97" dur="3s" repeatCount="1"></animate></polygon></g><g transform="translate(40, 520)"><g transform="translate(273, 0)"><rect x="0" y="37.916666666666664" width="21.666666666666668" height="21.666666666666668" fill="#3178c6" class="stroke-bg" stroke-width="1px"><animate attributeName="fill-opacity" values="0;0.2;0.4;0.6;0.8;1;1;1;1;1;1" dur="3s" repeatCount="1"></animate></rect><rect x="0" y="70.41666666666667" width="21.666666666666668" height="21.666666666666668" fill="#4F5D95" class="stroke-bg" stroke-width="1px"><animate attributeName="fill-opacity" values="0;0;0.2;0.4;0.6;0.8;1;1;1;1;1" dur="3s" repeatCount="1"></animate></rect><rect x="0" y="102.91666666666667" width="21.666666666666668" height="21.666666666666668" fill="#41b883" class="stroke-bg" stroke-width="1px"><animate attributeName="fill-opacity" values="0;0;0;0.2;0.4;0.6;0.8;1;1;1;1" dur="3s" repeatCount="1"></animate></rect><rect x="0" y="135.41666666666666" width="21.666666666666668" height="21.666666666666668" fill="#f7523f" class="stroke-bg" stroke-width="1px"><animate attributeName="fill-opacity" values="0;0;0;0;0.2;0.4;0.6;0.8;1;1;1" dur="3s" repeatCount="1"></animate></rect><rect x="0" y="167.91666666666666" width="21.666666666666668" height="21.666666666666668" fill="#f1e05a" class="stroke-bg" stroke-width="1px"><animate attributeName="fill-opacity" values="0;0;0;0;0;0.2;0.4;0.6;0.8;1;1" dur="3s" repeatCount="1"></animate></rect><rect x="0" y="200.41666666666666" width="21.666666666666668" height="21.666666666666668" fill="#444444" class="stroke-bg" stroke-width="1px"><animate attributeName="fill-opacity" values="0;0;0;0;0;0;0.2;0.4;0.6;0.8;1" dur="3s" repeatCount="1"></animate></rect><text dominant-baseline="middle" x="26" y="48.75" class="fill-fg" font-size="21.666666666666668px">TypeScript<animate attributeName="fill-opacity" values="0;0.2;0.4;0.6;0.8;1;1;1;1;1;1" dur="3s" repeatCount="1"></animate></text><text dominant-baseline="middle" x="26" y="81.25" class="fill-fg" font-size="21.666666666666668px">PHP<animate attributeName="fill-opacity" values="0;0;0.2;0.4;0.6;0.8;1;1;1;1;1" dur="3s" repeatCount="1"></animate></text><text dominant-baseline="middle" x="26" y="113.75" class="fill-fg" font-size="21.666666666666668px">Vue<animate attributeName="fill-opacity" values="0;0;0;0.2;0.4;0.6;0.8;1;1;1;1" dur="3s" repeatCount="1"></animate></text><text dominant-baseline="middle" x="26" y="146.25" class="fill-fg" font-size="21.666666666666668px">Blade<animate attributeName="fill-opacity" values="0;0;0;0;0.2;0.4;0.6;0.8;1;1;1" dur="3s" repeatCount="1"></animate></text><text dominant-baseline="middle" x="26" y="178.75" class="fill-fg" font-size="21.666666666666668px">JavaScript<animate attributeName="fill-opacity" values="0;0;0;0;0;0.2;0.4;0.6;0.8;1;1" dur="3s" repeatCount="1"></animate></text><text dominant-baseline="middle" x="26" y="211.25" class="fill-fg" font-size="21.666666666666668px">other<animate attributeName="fill-opacity" values="0;0;0;0;0;0;0.2;0.4;0.6;0.8;1" dur="3s" repeatCount="1"></animate></text></g><g transform="translate(130, 130)"><path d="M0,-117A117,117,0,1,1,-22.366,114.842L-12.425,63.801A65,65,0,1,0,0,-65Z" style="fill: #3178c6;" class="stroke-bg" stroke-width="2px"><title>TypeScript 234</title><animate attributeName="fill-opacity" values="0;0.2;0.4;0.6;0.8;1;1;1;1;1;1" dur="3s" repeatCount="1"></animate></path><path d="M-22.366,114.842A117,117,0,0,1,-104.115,-53.377L-57.842,-29.654A65,65,0,0,0,-12.425,63.801Z" style="fill: #4F5D95;" class="stroke-bg" stroke-width="2px"><title>PHP 130</title><animate attributeName="fill-opacity" values="0;0;0.2;0.4;0.6;0.8;1;1;1;1;1" dur="3s" repeatCount="1"></animate></path><path d="M-104.115,-53.377A117,117,0,0,1,-99.177,-62.071L-55.099,-34.484A65,65,0,0,0,-57.842,-29.654Z" style="fill: #41b883;" class="stroke-bg" stroke-width="2px"><title>Vue 6</title><animate attributeName="fill-opacity" values="0;0;0;0.2;0.4;0.6;0.8;1;1;1;1" dur="3s" repeatCount="1"></animate></path><path d="M-99.177,-62.071A117,117,0,0,1,-95.481,-67.62L-53.045,-37.566A65,65,0,0,0,-55.099,-34.484Z" style="fill: #f7523f;" class="stroke-bg" stroke-width="2px"><title>Blade 4</title><animate attributeName="fill-opacity" values="0;0;0;0;0.2;0.4;0.6;0.8;1;1;1" dur="3s" repeatCount="1"></animate></path><path d="M-95.481,-67.62A117,117,0,0,1,-92.504,-71.638L-51.391,-39.799A65,65,0,0,0,-53.045,-37.566Z" style="fill: #f1e05a;" class="stroke-bg" stroke-width="2px"><title>JavaScript 3</title><animate attributeName="fill-opacity" values="0;0;0;0;0;0.2;0.4;0.6;0.8;1;1" dur="3s" repeatCount="1"></animate></path><path d="M-92.504,-71.638A117,117,0,0,1,0,-117L0,-65A65,65,0,0,0,-51.391,-39.799Z" style="fill: #444444;" class="stroke-bg" stroke-width="2px"><title>other 64</title><animate attributeName="fill-opacity" values="0;0;0;0;0;0;0.2;0.4;0.6;0.8;1" dur="3s" repeatCount="1"></animate></path></g></g><g><text style="font-size: 32px; font-weight: bold;" x="384" y="830" text-anchor="end" class="fill-strong">533</text><text style="font-size: 24px;" x="394" y="830" text-anchor="start" class="fill-fg">contributions</text><g transform="translate(608, 802), scale(2)"><path fill-rule="evenodd" d="M8 .25a.75.75 0 01.673.418l1.882 3.815 4.21.612a.75.75 0 01.416 1.279l-3.046 2.97.719 4.192a.75.75 0 01-1.088.791L8 12.347l-3.766 1.98a.75.75 0 01-1.088-.79l.72-4.194L.818 6.374a.75.75 0 01.416-1.28l4.21-.611L7.327.668A.75.75 0 018 .25zm0 2.445L6.615 5.5a.75.75 0 01-.564.41l-3.097.45 2.24 2.184a.75.75 0 01.216.664l-.528 3.084 2.769-1.456a.75.75 0 01.698 0l2.77 1.456-.53-3.084a.75.75 0 01.216-.664l2.24-2.183-3.096-.45a.75.75 0 01-.564-.41L8 2.694v.001z" class="fill-fg"></path></g><text style="font-size: 32px; font-weight: bold;" x="650" y="830" text-anchor="start" class="fill-fg">5<title>5</title></text><g transform="translate(736, 802), scale(2)"><path fill-rule="evenodd" d="M5 3.25a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm0 2.122a2.25 2.25 0 10-1.5 0v.878A2.25 2.25 0 005.75 8.5h1.5v2.128a2.251 2.251 0 101.5 0V8.5h1.5a2.25 2.25 0 002.25-2.25v-.878a2.25 2.25 0 10-1.5 0v.878a.75.75 0 01-.75.75h-4.5A.75.75 0 015 6.25v-.878zm3.75 7.378a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm3-8.75a.75.75 0 100-1.5.75.75 0 000 1.5z" class="fill-fg"></path></g><text style="font-size: 32px; font-weight: bold;" x="772" y="830" text-anchor="start" class="fill-fg">0<title>0</title></text><text style="font-size: 16px;" x="1260" y="20" dominant-baseline="hanging" text-anchor="end" class="fill-weak">2024-07-21 / 2025-07-26</text></g></svg>